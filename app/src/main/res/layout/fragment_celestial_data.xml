<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_margin="20dp"
    android:padding="20dp"
    app:cardCornerRadius="@dimen/cardCornerRadius"
    app:cardElevation="20dp"
    tools:context=".ui.fragment.dialog.BirthDataDialogFragment">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="20dp"
        android:paddingTop="20dp"
        android:paddingEnd="20dp"
        android:paddingBottom="20dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="天象盤資訊"
            android:textColor="@color/colorPrimary"
            android:textSize="20sp"
            android:textStyle="bold" />

        <com.google.android.material.textfield.TextInputLayout
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:hint="請選擇日期"
            android:textColorHint="@color/primary_text"
            app:backgroundTint="@color/colorPrimary"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="1dp"
            app:endIconTint="@color/colorPrimary"
            app:hintTextAppearance="@style/ErrorTextAppearance"
            app:hintTextColor="@color/colorPrimary"
            app:startIconDrawable="@drawable/ic_baseline_date_range_24"
            app:startIconTint="@color/colorPrimary">

            <AutoCompleteTextView
                android:id="@+id/tvDate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:gravity="start"
                android:importantForAutofill="no"
                android:inputType="text"
                android:labelFor="@+id/etDegree120"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:hint="請選擇地點"
            android:textColorHint="@color/primary_text"
            app:backgroundTint="@color/colorPrimary"
            app:boxBackgroundColor="@color/white"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="1dp"
            app:endIconTint="@color/colorPrimary"
            app:hintTextAppearance="@style/ErrorTextAppearance"
            app:hintTextColor="@color/colorPrimary"
            app:startIconDrawable="@drawable/ic_round_location_on_24"
            app:startIconTint="@color/colorPrimary">

            <AutoCompleteTextView
                android:id="@+id/tvBirthPlace"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:gravity="start"
                android:importantForAutofill="no"
                android:inputType="text"
                android:maxLines="2"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp" />
        </com.google.android.material.textfield.TextInputLayout>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btSave"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/btn_primary_dark"
            android:text="確定"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </LinearLayout>


</com.google.android.material.card.MaterialCardView>