package com.one.astrology

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationChannelGroup
import android.app.NotificationManager
import android.content.Context
import com.facebook.ads.AudienceNetworkAds
import com.getkeepsafe.relinker.ReLinker
import com.google.android.gms.ads.MobileAds
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.util.CrashHandler
import com.one.core.util.LogUtil
import dagger.hilt.android.HiltAndroidApp

@HiltAndroidApp
class App : Application() {

    override fun onCreate() {
        super.onCreate()
        if (BuildConfig.DEBUG || BuildConfig.IS_DEV) {
            CrashHandler.instance.init()
        }
        // 使用 ReLinker 預先載入 ObjectBox 的 native lib
        ReLinker.loadLibrary(this, "objectbox-jni")
        ObjectBox.init(this)
        LiveEventBus.config().autoClear(true).lifecycleObserverAlwaysActive(false)
        MobileAds.initialize(this) {}
        AudienceNetworkAds.initialize(this)

        createNotificationChannel()
    }

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        LogUtil.setDebug(BuildConfig.DEBUG)
    }


    private fun createNotificationChannel() {
        val channelMessage = NotificationChannel(
            getString(R.string.channel_id),
            getString(R.string.channel_name),
            NotificationManager.IMPORTANCE_HIGH
        )

        val notificationManager: NotificationManager =
            this.getSystemService(NotificationManager::class.java)
        val notificationChannelGroup = NotificationChannelGroup(
            getString(R.string.channel_group_id),
            getString(R.string.channel_group_name)
        )
        notificationManager.createNotificationChannelGroup(notificationChannelGroup)
        channelMessage.group = getString(R.string.channel_group_id)

        notificationManager.createNotificationChannel(channelMessage)
    }
}