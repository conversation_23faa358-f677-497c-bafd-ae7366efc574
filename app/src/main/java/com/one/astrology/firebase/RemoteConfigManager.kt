package com.one.astrology.firebase

import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings
import com.google.gson.Gson
import com.one.astrology.R
import com.one.astrology.data.model.Announcement
import com.one.core.util.LogUtil
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class RemoteConfigManager @Inject constructor() {
    private val remoteConfig: FirebaseRemoteConfig = FirebaseRemoteConfig.getInstance()
    private val configSettings: FirebaseRemoteConfigSettings = FirebaseRemoteConfigSettings.Builder()
        .setMinimumFetchIntervalInSeconds(3600) // 1小時
        .build()

    private val _announcement = MutableStateFlow<List<Announcement>>(emptyList())
    val announcement: StateFlow<List<Announcement>> = _announcement

    init {
        remoteConfig.setConfigSettingsAsync(configSettings)
        remoteConfig.setDefaultsAsync(R.xml.remote_config_defaults)
        fetchConfig()
    }

    private fun fetchConfig() {
        remoteConfig.fetchAndActivate().addOnCompleteListener { task ->
            if (task.isSuccessful) {
                val announcementJson = remoteConfig.getString("latest_announcement")
                try {
                    val announcements = Gson().fromJson(announcementJson, Array<Announcement>::class.java).toList()
                    _announcement.value = announcements
                } catch (e: Exception) {
                    e.message?.let { LogUtil.e("解析公告失敗", it) }
                    _announcement.value = emptyList()
                }
            }
        }
    }

    fun refreshAnnouncement() {
        fetchConfig()
    }
} 