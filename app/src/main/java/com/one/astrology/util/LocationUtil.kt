package com.one.astrology.util

import android.app.Activity
import android.content.Context
import android.location.Address
import android.location.Geocoder
import android.location.Location
import android.os.Build
import android.os.Looper
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationCallback
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationResult
import com.google.android.gms.location.LocationServices
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.data.model.City
import com.one.core.util.LogUtil
import com.one.core.util.PermissionsUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.net.HttpURLConnection
import java.net.URL
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.util.Locale
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class LocationUtil {
    companion object {

        private const val NOMINATIM_API_URL = "https://nominatim.openstreetmap.org/search"

        fun getCurrentLocation(
            context: Activity,
            permissionCode: Int,
            onLocationReceived: (Location) -> Unit
        ) {
            val fusedLocationClient: FusedLocationProviderClient =
                LocationServices.getFusedLocationProviderClient(context)

            if (PermissionsUtil.checkLocationPermission(context, permissionCode)) {
                // 先嘗試取得最後已知位置
                fusedLocationClient.lastLocation.addOnSuccessListener { location ->
                    if (location != null) {
                        onLocationReceived(location)
                    } else {
                        // 若為 null，則啟用即時定位
                        val locationRequest = LocationRequest.create().apply {
                            priority = LocationRequest.PRIORITY_HIGH_ACCURACY
                            interval = 3000 // 每 3 秒更新
                            fastestInterval = 1000
                            numUpdates = 1 // 只需要一次
                        }

                        val locationCallback = object : LocationCallback() {
                            override fun onLocationResult(locationResult: LocationResult) {
                                locationResult.lastLocation?.let { freshLocation ->
                                    onLocationReceived(freshLocation)
                                }
                                // 用完就移除，避免耗電
                                fusedLocationClient.removeLocationUpdates(this)
                            }
                        }

                        fusedLocationClient.requestLocationUpdates(
                            locationRequest,
                            locationCallback,
                            Looper.getMainLooper()
                        )
                    }
                }
            }
        }


        val latLng = LatLng(25.047998, 121.554483)

        suspend fun addressToLatLng(context: Context, address: String): LatLng {
            if (address.isEmpty()) {
                return LatLng(25.047998, 121.554483)
            }

            return withContext(Dispatchers.IO) {
                try {
                    val geocoder = Geocoder(context, Locale.getDefault())

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        suspendCoroutine { continuation ->
                            geocoder.getFromLocationName(address, 1) { addresses ->
                                val latLng = if (addresses.isNotEmpty()) {
                                    LatLng(addresses[0].latitude, addresses[0].longitude)
                                } else {
                                    latLng
                                }
                                continuation.resume(latLng)
                            }
                        }
                    } else {
                        val addresses = geocoder.getFromLocationName(address, 1)
                        if (addresses.isNullOrEmpty()) {
                            latLng
                        } else {
                            LatLng(addresses[0].latitude, addresses[0].longitude)
                        }
                    }
                } catch (e: Exception) {
                    LogUtil.e(e.message ?: "Geocoder error")
                    latLng
                }
            }
        }


        private fun getAddresses(addresses: List<Address>): String {
            var addressString = ""
            if (addresses.isEmpty()) {
                return ""
            }
            //取得第一個資料
            val address = addresses[0]

            // 整串地址(110台灣台北市信義區信義路五段5號2C20室)
            LogUtil.d("getAddressLine : ", address.getAddressLine(0))
            if (!address.postalCode.isNullOrEmpty()) {
                // (110)
                LogUtil.d("postalCode : ", address.postalCode)
            }
            if (!address.countryName.isNullOrEmpty()) {
                // (台灣)
                LogUtil.d("countryName : ", address.countryName)
                addressString = address.countryName
            }
            if (!address.adminArea.isNullOrEmpty()) {
                addressString += address.adminArea
                // (台北市)
                LogUtil.d("adminArea : ", address.adminArea)
            }
            if (!address.locality.isNullOrEmpty()) {
                addressString += address.locality
                // (信義區)
                LogUtil.d("locality : ", address.locality)
            }
            if (!address.thoroughfare.isNullOrEmpty()) {
                addressString += address.thoroughfare
                // (信義路五段)
                LogUtil.d("thoroughfare : ", address.thoroughfare)
            }
            if (!address.featureName.isNullOrEmpty()) {
                addressString += address.featureName
                // (5號)
                LogUtil.d("featureName : ", address.featureName)
            }
            return addressString
        }

        suspend fun searchCitySuggestions(query: String): List<City> = withContext(Dispatchers.IO) {
            try {
                val encodedQuery = URLEncoder.encode(query, StandardCharsets.UTF_8.toString())
                val url = "$NOMINATIM_API_URL?q=$encodedQuery&format=json&limit=5"

                val response = URL(url).readText()
                val jsonArray = JSONObject(response).getJSONArray("results")

                val cities = mutableListOf<City>()
                for (i in 0 until jsonArray.length()) {
                    val item = jsonArray.getJSONObject(i)
                    cities.add(
                        City(
                            name = item.getString("display_name").split(",")[0],
                            latitude = item.getDouble("lat"),
                            longitude = item.getDouble("lon"),
                            country = item.getString("display_name").split(",").lastOrNull()?.trim()
                                ?: ""
                        )
                    )
                }
                cities
            } catch (e: Exception) {
                emptyList()
            }
        }

        suspend fun latLngToAddresses(
            latLng: LatLng,
            fields: List<String>? = null,
            separator: String = " "
        ): String = withContext(Dispatchers.IO) {
            try {
                val url =
                    "https://nominatim.openstreetmap.org/reverse?lat=${latLng.latitude}&lon=${latLng.longitude}&format=json"
                val connection = URL(url).openConnection() as HttpURLConnection

                connection.setRequestProperty("User-Agent", "MyApp/1.0 (<EMAIL>)")
                connection.requestMethod = "GET"
                connection.connectTimeout = 5000
                connection.readTimeout = 5000

                val response = connection.inputStream.bufferedReader().use { it.readText() }
                val jsonObject = JSONObject(response)
                val addressObject = jsonObject.optJSONObject("address") ?: return@withContext ""

                val fullAddressMap = mapOf(
                    "country" to addressObject.optString("country", ""),
                    "city" to addressObject.optString("city", ""),
                    "suburb" to addressObject.optString("suburb", ""),
                    "neighbourhood" to addressObject.optString("neighbourhood", ""),
                    "road" to addressObject.optString("road", ""),
                    "house_number" to addressObject.optString("house_number", "")
                )

                val selectedValues = (fields ?: fullAddressMap.keys.toList())
                    .mapNotNull { fullAddressMap[it] }
                    .filter { it.isNotEmpty() }
                    .joinToString(separator)

                LogUtil.d("Selected address: $selectedValues")
                selectedValues
            } catch (e: Exception) {
                e.message?.let { LogUtil.e(it) }
                ""
            }
        }

        suspend fun latLngToArea(
            latLng: LatLng,
        ): String = withContext(Dispatchers.IO) {
            try {
                val url =
                    "$NOMINATIM_API_URL?lat=${latLng.latitude}&lon=${latLng.longitude}&format=json"
                val response = URL(url).readText()
                val jsonObject = JSONObject(response)
                val address = jsonObject.getString("display_name")
                return@withContext address
            } catch (e: Exception) {
                return@withContext ""
            }
        }
    }
}