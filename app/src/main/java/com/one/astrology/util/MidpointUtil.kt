package com.one.astrology.util

import com.one.astrology.constant.Planet
import com.one.astrology.data.Houses
import com.one.astrology.data.bean.Aspect
import com.one.astrology.data.bean.AspectBean
import com.one.astrology.data.bean.AspectType
import com.one.astrology.data.bean.MidpointBean
import com.one.astrology.data.bean.PlanetBean
import kotlin.math.abs

/**
 * 中點計算工具類
 * 用於計算和分析中點
 */
object MidpointUtil {

    /**
     * 計算兩個行星或點位之間的中點
     * @param firstPlanet 第一個行星或點位
     * @param secondPlanet 第二個行星或點位
     * @param houses 宮位數據
     * @return 中點數據
     */
    fun calculateMidpoint(
        firstPlanet: PlanetBean,
        secondPlanet: PlanetBean,
        houses: Houses
    ): MidpointBean {
        val midpointAngle = calculateMidpointAngle(firstPlanet.longitude, secondPlanet.longitude)
        val signId = getSignFromAngle(midpointAngle)
        val houseId = ArabicPartUtil.getHouse(midpointAngle, houses)
        
        return MidpointBean(
            id = MidpointBean.createMidpointId(firstPlanet.id, secondPlanet.id),
            firstPointId = firstPlanet.id,
            secondPointId = secondPlanet.id,
            angle = midpointAngle,
            signId = signId,
            houseId = houseId
        )
    }

    /**
     * 計算兩個角度之間的中點
     * @param angle1 第一個角度
     * @param angle2 第二個角度
     * @return 中點角度（0-360度）
     */
    fun calculateMidpointAngle(angle1: Double, angle2: Double): Double {
        // 標準化角度到0-360度範圍
        val normalizedAngle1 = normalizeAngle(angle1)
        val normalizedAngle2 = normalizeAngle(angle2)
        
        // 計算兩個角度之間的差值
        val diff = abs(normalizedAngle1 - normalizedAngle2)
        
        // 如果差值大於180度，則需要特殊處理
        if (diff > 180.0) {
            // 計算中點（考慮跨越0度的情況）
            val sum = normalizedAngle1 + normalizedAngle2
            return normalizeAngle(sum / 2.0 + 180.0)
        } else {
            // 正常計算中點
            val sum = normalizedAngle1 + normalizedAngle2
            return normalizeAngle(sum / 2.0)
        }
    }

    /**
     * 計算所有行星之間的中點
     * @param planets 行星列表
     * @param houses 宮位數據
     * @return 中點列表
     */
    fun calculateAllMidpoints(
        planets: List<PlanetBean>,
        houses: Houses
    ): List<MidpointBean> {
        // 如果行星列表為空或只有一個行星，直接返回空列表
        if (planets.size <= 1) return emptyList()
        
        // 使用 buildList 構建器提高效率
        return buildList {
            // 計算所有行星對之間的中點
            for (i in planets.indices) {
                for (j in i + 1 until planets.size) {
                    val firstPlanet = planets[i]
                    val secondPlanet = planets[j]
                    
                    // 跳過不需要計算的行星組合（可根據需求自定義）
                    if (shouldSkipMidpoint(firstPlanet.id, secondPlanet.id)) {
                        continue
                    }
                    
                    add(calculateMidpoint(firstPlanet, secondPlanet, houses))
                }
            }
        }
    }

    /**
     * 判斷是否應該跳過某些行星組合的中點計算
     * @param firstPlanetId 第一個行星ID
     * @param secondPlanetId 第二個行星ID
     * @return 是否跳過
     */
    private fun shouldSkipMidpoint(firstPlanetId: Int, secondPlanetId: Int): Boolean {
        // 這裡可以根據實際需求自定義跳過規則
        // 例如：跳過某些不重要的小行星組合
        
        // 默認不跳過任何組合
        return false
    }

    /**
     * 計算中點與行星之間的相位
     * @param midpoint 中點
     * @param planets 行星列表
     * @param orbs 容許度（相位誤差範圍）
     * @return 更新後的中點（包含相位信息）
     */
    fun calculateMidpointAspects(
        midpoint: MidpointBean,
        planets: List<PlanetBean>,
        orbs: Map<Int, Double> = defaultOrbs()
    ): MidpointBean {
        // 使用 buildList 構建器提高效率
        val aspectBeans = buildList {
            for (planet in planets) {
                // 跳過與構成中點的行星計算相位
                if (planet.id == midpoint.firstPointId || planet.id == midpoint.secondPointId) {
                    continue
                }
                
                // 計算中點與行星之間的相位
                val aspectType = getAspectType(midpoint.angle, planet.longitude, orbs)
                if (aspectType != Aspect.NONE) {
                    // 只有當存在有效相位時才進行後續計算
                    val distance = ArabicPartUtil.calculateAngleDistance(midpoint.angle, planet.longitude)
                    val orb = abs(distance - getAspectAngle(aspectType))
                    // 由於 midpoint 不是 PlanetBean，我們使用簡化版的 isApplying 方法
                    val isApplying = isApplying(midpoint.angle, planet.longitude, aspectType)
                    
                    add(
                        AspectBean(
                            firstPointId = -1, // 使用-1表示這是中點
                            secondPointId = planet.id,
                            aspectType = aspectType,
                            orb = orb,
                            isApplying = isApplying
                        )
                    )
                }
            }
        }
        
        // 使用 buildList 構建器和 mapTo 提高效率
        val aspects = buildList(aspectBeans.size) {
            aspectBeans.mapTo(this) { aspectBean ->
                Aspect(
                    type = getAspectTypeEnum(aspectBean.aspectType),
                    planet = createPlanetBean(aspectBean.secondPointId),
                    isDraw = true,
                    direction = if (aspectBean.isApplying) "applying" else "separating"
                ).apply {
                    orb = aspectBean.orb
                }
            }
        }
        
        // 使用 copy 創建新的 MidpointBean 對象，保持不變性
        return midpoint.copy(aspects = aspects)
    }

    /**
     * 計算所有中點與行星之間的相位
     * @param midpoints 中點列表
     * @param planets 行星列表
     * @param orbs 容許度（相位誤差範圍）
     * @return 更新後的中點列表（包含相位信息）
     */
    fun calculateAllMidpointAspects(
        midpoints: List<MidpointBean>,
        planets: List<PlanetBean>,
        orbs: Map<Int, Double> = defaultOrbs()
    ): List<MidpointBean> {
        // 如果中點列表為空，直接返回空列表
        if (midpoints.isEmpty()) return emptyList()
        
        // 對於大量數據，使用並行處理提高效率
        return if (midpoints.size > 10) {
            midpoints.parallelStream()
                .map { midpoint -> calculateMidpointAspects(midpoint, planets, orbs) }
                .toList()
        } else {
            // 對於少量數據，使用普通的 map 操作
            midpoints.map { midpoint -> calculateMidpointAspects(midpoint, planets, orbs) }
        }
    }

    /**
     * 獲取相位類型
     * @param angle1 第一個角度
     * @param angle2 第二個角度
     * @param orbs 容許度（相位誤差範圍）
     * @return 相位類型
     */
    private fun getAspectType(
        angle1: Double,
        angle2: Double,
        orbs: Map<Int, Double>
    ): Int {
        val distance = ArabicPartUtil.calculateAngleDistance(angle1, angle2)
        
        // 使用 when 表達式簡化代碼
        return when {
            isWithinOrb(distance, 0.0, orbs[Aspect.CONJUNCTION] ?: 8.0) -> Aspect.CONJUNCTION
            isWithinOrb(distance, 60.0, orbs[Aspect.SEXTILE] ?: 4.0) -> Aspect.SEXTILE
            isWithinOrb(distance, 90.0, orbs[Aspect.SQUARE] ?: 6.0) -> Aspect.SQUARE
            isWithinOrb(distance, 120.0, orbs[Aspect.TRINE] ?: 6.0) -> Aspect.TRINE
            isWithinOrb(distance, 180.0, orbs[Aspect.OPPOSITION] ?: 8.0) -> Aspect.OPPOSITION
            isWithinOrb(distance, 30.0, orbs[Aspect.SEMI_SEXTILE] ?: 2.0) -> Aspect.SEMI_SEXTILE
            isWithinOrb(distance, 45.0, orbs[Aspect.SEMI_SQUARE] ?: 2.0) -> Aspect.SEMI_SQUARE
            isWithinOrb(distance, 135.0, orbs[Aspect.SESQUI_SQUARE] ?: 2.0) -> Aspect.SESQUI_SQUARE
            isWithinOrb(distance, 150.0, orbs[Aspect.QUINCUNX] ?: 3.0) -> Aspect.QUINCUNX
            else -> Aspect.NONE
        }
    }

    /**
     * 判斷角度是否在容許度範圍內
     * @param distance 角度距離
     * @param aspectAngle 相位角度
     * @param orb 容許度
     * @return 是否在容許度範圍內
     */
    private fun isWithinOrb(distance: Double, aspectAngle: Double, orb: Double): Boolean {
        return abs(distance - aspectAngle) <= orb
    }

    /**
     * 獲取相位角度
     * @param aspectType 相位類型
     * @return 相位角度
     */
    private fun getAspectAngle(aspectType: Int): Double {
        return when (aspectType) {
            Aspect.CONJUNCTION -> 0.0
            Aspect.SEXTILE -> 60.0
            Aspect.SQUARE -> 90.0
            Aspect.TRINE -> 120.0
            Aspect.OPPOSITION -> 180.0
            Aspect.SEMI_SEXTILE -> 30.0
            Aspect.SEMI_SQUARE -> 45.0
            Aspect.SESQUI_SQUARE -> 135.0
            Aspect.QUINCUNX -> 150.0
            else -> 0.0
        }
    }

    /**
     * 判斷相位是否正在形成（applying）
     * @param angle1 第一個角度
     * @param angle2 第二個角度
     * @param aspectType 相位類型
     * @return 是否正在形成
     */
    private fun isApplying(angle1: Double, angle2: Double, aspectType: Int): Boolean {
        // 標準化角度
        val normalizedAngle1 = normalizeAngle(angle1)
        val normalizedAngle2 = normalizeAngle(angle2)
        
        // 計算理想相位角度
        val aspectAngle = getAspectAngle(aspectType)
        
        // 計算當前角度差
        val currentDiff = ArabicPartUtil.calculateAngleDistance(normalizedAngle1, normalizedAngle2)
        
        // 在實際應用中，應該考慮行星的速度
        // 例如：如果第一個天體的速度比第二個天體快，且角度差小於理想相位角度，則相位正在形成
        // 如果第一個天體的速度比第二個天體慢，且角度差大於理想相位角度，則相位正在形成
        
        // 這裡我們使用一個簡化的判斷方法
        // 如果角度差接近理想相位角度（誤差小於0.5度），且角度差小於理想相位角度，則認為相位正在形成
        return abs(currentDiff - aspectAngle) < 0.5 && currentDiff < aspectAngle
    }

    /**
     * 判斷相位是否正在形成（applying），考慮行星速度
     * @param planet1 第一個行星
     * @param planet2 第二個行星
     * @param aspectType 相位類型
     * @return 是否正在形成
     */
    private fun isApplyingWithSpeed(planet1: PlanetBean, planet2: PlanetBean, aspectType: Int): Boolean {
        // 標準化角度
        val angle1 = normalizeAngle(planet1.longitude)
        val angle2 = normalizeAngle(planet2.longitude)
        
        // 計算理想相位角度
        val aspectAngle = getAspectAngle(aspectType)
        
        // 計算當前角度差
        val currentDiff = ArabicPartUtil.calculateAngleDistance(angle1, angle2)
        
        // 獲取行星速度
        val speed1 = planet1.speed
        val speed2 = planet2.speed
        
        // 計算相對速度（正值表示第一個行星比第二個行星移動得快）
        val relativeSpeed = speed1 - speed2
        
        // 判斷相位是否正在形成
        // 如果相對速度為正，且當前角度差小於理想相位角度，則相位正在形成
        // 如果相對速度為負，且當前角度差大於理想相位角度，則相位正在形成
        return if (relativeSpeed > 0) {
            currentDiff < aspectAngle
        } else {
            currentDiff > aspectAngle
        }
    }

    /**
     * 獲取默認的容許度設置
     * @return 容許度映射
     */
    private fun defaultOrbs(): Map<Int, Double> {
        return mapOf(
            Aspect.CONJUNCTION to 8.0,
            Aspect.OPPOSITION to 8.0,
            Aspect.TRINE to 6.0,
            Aspect.SQUARE to 6.0,
            Aspect.SEXTILE to 4.0,
            Aspect.SEMI_SEXTILE to 2.0,
            Aspect.SEMI_SQUARE to 2.0,
            Aspect.SESQUI_SQUARE to 2.0,
            Aspect.QUINCUNX to 3.0
        )
    }

    /**
     * 標準化角度到0-360度範圍
     * @param angle 角度
     * @return 標準化後的角度
     */
    private fun normalizeAngle(angle: Double): Double {
        var result = angle % 360.0
        if (result < 0) {
            result += 360.0
        }
        return result
    }

    /**
     * 根據角度獲取星座ID
     * @param angle 角度
     * @return 星座ID (1-12)
     */
    private fun getSignFromAngle(angle: Double): Int {
        return (angle / 30.0).toInt() + 1
    }

    /**
     * 獲取中點解釋
     * @param midpoint 中點
     * @return 解釋文本
     */
    fun getMidpointInterpretation(midpoint: MidpointBean): String {
        // 這裡可以實現中點解釋的邏輯
        // 可以根據中點的行星組合、所在星座、宮位以及與其他行星的相位來生成解釋
        
        // 簡單示例
        val firstPlanetName = getPlanetName(midpoint.firstPointId)
        val secondPlanetName = getPlanetName(midpoint.secondPointId)
        val signName = getSignName(midpoint.signId)
        val houseName = getHouseName(midpoint.houseId)
        
        return "「$firstPlanetName/$secondPlanetName」中點位於${signName}座，第${houseName}宮。" +
                "這表示${getMidpointMeaning(midpoint.firstPointId, midpoint.secondPointId)}。" +
                "在${getSignMeaning(midpoint.signId)}的影響下，" +
                "這種能量主要表現在${getHouseMeaning(midpoint.houseId)}領域。"
    }

    /**
     * 獲取行星名稱
     * @param planetId 行星ID
     * @return 行星名稱
     */
    private fun getPlanetName(planetId: Int): String {
        return when (planetId) {
            Planet.SUN -> "太陽"
            Planet.MOON -> "月亮"
            Planet.MERCURY -> "水星"
            Planet.VENUS -> "金星"
            Planet.MARS -> "火星"
            Planet.JUPITER -> "木星"
            Planet.SATURN -> "土星"
            Planet.URANUS -> "天王星"
            Planet.NEPTUNE -> "海王星"
            Planet.PLUTO -> "冥王星"
            else -> "未知行星"
        }
    }

    /**
     * 獲取星座名稱
     * @param signId 星座ID
     * @return 星座名稱
     */
    private fun getSignName(signId: Int): String {
        return when (signId) {
            1 -> "白羊"
            2 -> "金牛"
            3 -> "雙子"
            4 -> "巨蟹"
            5 -> "獅子"
            6 -> "處女"
            7 -> "天秤"
            8 -> "天蠍"
            9 -> "射手"
            10 -> "摩羯"
            11 -> "水瓶"
            12 -> "雙魚"
            else -> "未知星座"
        }
    }

    /**
     * 獲取宮位名稱
     * @param houseId 宮位ID
     * @return 宮位名稱
     */
    private fun getHouseName(houseId: Int): String {
        return "$houseId"
    }

    /**
     * 獲取中點意義
     * @param firstPlanetId 第一個行星ID
     * @param secondPlanetId 第二個行星ID
     * @return 中點意義
     */
    private fun getMidpointMeaning(firstPlanetId: Int, secondPlanetId: Int): String {
        // 這裡可以實現中點意義的邏輯
        // 可以根據行星組合來生成意義
        
        // 簡單示例
        return when {
            (firstPlanetId == Planet.SUN && secondPlanetId == Planet.MOON) ||
            (firstPlanetId == Planet.MOON && secondPlanetId == Planet.SUN) ->
                "個性與情感的融合，表示個人的核心情感需求和自我表達方式"
            
            (firstPlanetId == Planet.SUN && secondPlanetId == Planet.MERCURY) ||
            (firstPlanetId == Planet.MERCURY && secondPlanetId == Planet.SUN) ->
                "思想與意識的結合，表示個人的思維方式和自我認知"
            
            (firstPlanetId == Planet.VENUS && secondPlanetId == Planet.MARS) ||
            (firstPlanetId == Planet.MARS && secondPlanetId == Planet.VENUS) ->
                "愛與慾望的平衡，表示個人的情感表達和行動力之間的關係"
            
            else -> "這兩個行星能量的結合，代表著它們各自特質的融合與平衡"
        }
    }

    /**
     * 獲取星座意義
     * @param signId 星座ID
     * @return 星座意義
     */
    private fun getSignMeaning(signId: Int): String {
        return when (signId) {
            1 -> "牡羊座的積極主動和勇往直前"
            2 -> "金牛座的穩定和實際"
            3 -> "雙子座的靈活和好奇"
            4 -> "巨蟹座的情感豐富和保護性"
            5 -> "獅子座的自信和創造力"
            6 -> "處女座的分析和完美主義"
            7 -> "天秤座的和諧和平衡"
            8 -> "天蠍座的深度和轉化"
            9 -> "射手座的探索和擴張"
            10 -> "摩羯座的責任和成就"
            11 -> "水瓶座的創新和獨立"
            12 -> "雙魚座的直覺和同理心"
            else -> "未知星座的影響"
        }
    }

    /**
     * 獲取宮位意義
     * @param houseId 宮位ID
     * @return 宮位意義
     */
    private fun getHouseMeaning(houseId: Int): String {
        return when (houseId) {
            1 -> "個人形象和自我表達"
            2 -> "個人價值和物質資源"
            3 -> "溝通和學習"
            4 -> "家庭和情感基礎"
            5 -> "創造力和娛樂"
            6 -> "工作和健康"
            7 -> "關係和合作"
            8 -> "共享資源和轉化"
            9 -> "高等教育和信仰"
            10 -> "職業和社會地位"
            11 -> "友誼和社交圈"
            12 -> "潛意識和精神世界"
            else -> "未知領域"
        }
    }

    /**
     * 根據相位類型整數值獲取對應的 AspectType 枚舉
     * @param aspectTypeValue 相位類型整數值
     * @return 相位類型枚舉
     */
    private fun getAspectTypeEnum(aspectTypeValue: Int): AspectType {
        return when (aspectTypeValue) {
            Aspect.CONJUNCTION -> AspectType.Conjunction
            Aspect.SEXTILE -> AspectType.Sextile
            Aspect.SQUARE -> AspectType.Square
            Aspect.TRINE -> AspectType.Trine
            Aspect.OPPOSITION -> AspectType.Opposition
            Aspect.SEMI_SEXTILE -> AspectType.SemiSextile
            Aspect.SEMI_SQUARE -> AspectType.SemiSquare
            Aspect.SESQUI_SQUARE -> AspectType.Sesquiquadrate
            Aspect.QUINCUNX -> AspectType.Quincunx
            else -> AspectType.Conjunction // 默認為合相
        }
    }

    /**
     * 創建簡單的 PlanetBean 對象
     * @param planetId 行星ID
     * @return PlanetBean 對象
     */
    private fun createPlanetBean(planetId: Int): PlanetBean {
        val planetName = getPlanetName(planetId)
        // 根據行星ID獲取適當的符號和顏色
        val (symbol, color) = when (planetId) {
            Planet.SUN -> "☉" to "#FFA500" // 橙色
            Planet.MOON -> "☽" to "#C0C0C0" // 銀色
            Planet.MERCURY -> "☿" to "#808080" // 灰色
            Planet.VENUS -> "♀" to "#00FF00" // 綠色
            Planet.MARS -> "♂" to "#FF0000" // 紅色
            Planet.JUPITER -> "♃" to "#800080" // 紫色
            Planet.SATURN -> "♄" to "#A52A2A" // 棕色
            Planet.URANUS -> "♅" to "#00FFFF" // 青色
            Planet.NEPTUNE -> "♆" to "#0000FF" // 藍色
            Planet.PLUTO -> "♇" to "#000000" // 黑色
            else -> "" to "#000000" // 默認黑色
        }
        
        return PlanetBean(
            id = planetId,
            symbol = symbol,
            enName = planetName, // 使用中文名稱作為英文名稱，簡化處理
            enSimpleName = symbol, 
            chName = planetName,
            color = color
        )
    }
} 