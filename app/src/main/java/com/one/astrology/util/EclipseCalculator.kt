package com.one.astrology.util

import android.content.Context
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.data.model.*
import com.one.core.util.LogUtil
import swisseph.SweConst
import swisseph.SwissEph
import java.util.*
import kotlin.math.*

/**
 * 專業的日月蝕計算器
 * 基於 Swiss Ephemeris 的蝕相計算功能
 * 參考：https://www.astro.com/swisseph/swephprg.htm#_Hlk477326807
 */
object EclipseCalculator {

    /**
     * 計算指定日期範圍內的所有蝕相事件
     */
    fun calculateEclipsesInRange(
        context: Context,
        startDate: Date,
        endDate: Date,
        location: LatLng = LatLng(0.0, 0.0)
    ): List<AstrologyEvent> {
        val events = mutableListOf<AstrologyEvent>()
        
        try {
            // 初始化 Swiss Ephemeris
            if (!EphemerisUtil.initData(context)) {
                LogUtil.e("Failed to initialize Swiss Ephemeris for eclipse calculation")
                return emptyList()
            }
            
            val startJulDay = EphemerisUtil.getJulDay(startDate.time, location)
            val endJulDay = EphemerisUtil.getJulDay(endDate.time, location)
            
            // 計算日蝕
            events.addAll(calculateSolarEclipses(context, startJulDay, endJulDay, location))
            
            // 計算月蝕
            events.addAll(calculateLunarEclipses(context, startJulDay, endJulDay, location))
            
        } catch (e: Exception) {
            LogUtil.e("計算蝕相事件範圍時發生錯誤: ${e.message}")
        }
        
        return events.sortedBy { it.date }
    }

    /**
     * 計算指定日期的蝕相事件
     */
    fun calculateEclipseForDate(
        context: Context,
        date: Date,
        location: LatLng = LatLng(0.0, 0.0)
    ): List<AstrologyEvent> {
        val events = mutableListOf<AstrologyEvent>()
        
        try {
            if (!EphemerisUtil.initData(context)) {
                LogUtil.e("Failed to initialize Swiss Ephemeris")
                return emptyList()
            }
            
            val julDay = EphemerisUtil.getJulDay(date.time, location)
            
            // 檢查當天是否有蝕相（容許度±1天）
            val dayBefore = julDay - 1.0
            val dayAfter = julDay + 1.0
            
            // 檢查日蝕
            val solarEclipse = findNearestSolarEclipse(context, julDay, dayBefore, dayAfter, location)
            if (solarEclipse != null) {
                events.add(solarEclipse)
            }
            
            // 檢查月蝕
            val lunarEclipse = findNearestLunarEclipse(context, julDay, dayBefore, dayAfter, location)
            if (lunarEclipse != null) {
                events.add(lunarEclipse)
            }
            
        } catch (e: Exception) {
            LogUtil.e("計算指定日期蝕相時發生錯誤: ${e.message}")
        }
        
        return events
    }

    /**
     * 計算日蝕事件
     */
    private fun calculateSolarEclipses(
        context: Context,
        startJulDay: Double,
        endJulDay: Double,
        location: LatLng
    ): List<AstrologyEvent> {
        val events = mutableListOf<AstrologyEvent>()
        
        try {
            var currentJulDay = startJulDay
            
            while (currentJulDay <= endJulDay) {
                // 尋找下一個新月
                val newMoonJulDay = findNextNewMoon(context, currentJulDay)
                
                if (newMoonJulDay > endJulDay) break
                
                // 檢查新月時是否發生日蝕
                val eclipse = checkSolarEclipseAtNewMoon(context, newMoonJulDay, location)
                if (eclipse != null) {
                    events.add(eclipse)
                }
                
                // 移動到下一個新月週期（約29.5天）
                currentJulDay = newMoonJulDay + 29.0
            }
            
        } catch (e: Exception) {
            LogUtil.e("計算日蝕事件時發生錯誤: ${e.message}")
        }
        
        return events
    }

    /**
     * 計算月蝕事件
     */
    private fun calculateLunarEclipses(
        context: Context,
        startJulDay: Double,
        endJulDay: Double,
        location: LatLng
    ): List<AstrologyEvent> {
        val events = mutableListOf<AstrologyEvent>()
        
        try {
            var currentJulDay = startJulDay
            
            while (currentJulDay <= endJulDay) {
                // 尋找下一個滿月
                val fullMoonJulDay = findNextFullMoon(context, currentJulDay)
                
                if (fullMoonJulDay > endJulDay) break
                
                // 檢查滿月時是否發生月蝕
                val eclipse = checkLunarEclipseAtFullMoon(context, fullMoonJulDay, location)
                if (eclipse != null) {
                    events.add(eclipse)
                }
                
                // 移動到下一個滿月週期（約29.5天）
                currentJulDay = fullMoonJulDay + 29.0
            }
            
        } catch (e: Exception) {
            LogUtil.e("計算月蝕事件時發生錯誤: ${e.message}")
        }
        
        return events
    }

    /**
     * 尋找最接近的日蝕
     */
    private fun findNearestSolarEclipse(
        context: Context,
        targetJulDay: Double,
        startJulDay: Double,
        endJulDay: Double,
        location: LatLng
    ): AstrologyEvent? {
        try {
            var currentJulDay = startJulDay
            
            while (currentJulDay <= endJulDay) {
                val eclipse = checkSolarEclipseAtTime(context, currentJulDay, location)
                if (eclipse != null) {
                    return eclipse
                }
                currentJulDay += 0.1 // 每0.1天檢查一次
            }
            
        } catch (e: Exception) {
            LogUtil.e("尋找最接近日蝕時發生錯誤: ${e.message}")
        }
        
        return null
    }

    /**
     * 尋找最接近的月蝕
     */
    private fun findNearestLunarEclipse(
        context: Context,
        targetJulDay: Double,
        startJulDay: Double,
        endJulDay: Double,
        location: LatLng
    ): AstrologyEvent? {
        try {
            var currentJulDay = startJulDay
            
            while (currentJulDay <= endJulDay) {
                val eclipse = checkLunarEclipseAtTime(context, currentJulDay, location)
                if (eclipse != null) {
                    return eclipse
                }
                currentJulDay += 0.1 // 每0.1天檢查一次
            }
            
        } catch (e: Exception) {
            LogUtil.e("尋找最接近月蝕時發生錯誤: ${e.message}")
        }
        
        return null
    }

    /**
     * 尋找下一個新月
     */
    private fun findNextNewMoon(context: Context, startJulDay: Double): Double {
        try {
            var currentJulDay = startJulDay
            var previousPhase = getMoonPhaseAngle(context, currentJulDay)
            
            // 尋找月相角度從大於180度變為小於180度的時刻（新月）
            for (i in 1..60) { // 最多搜索60天
                currentJulDay += 0.5
                val currentPhase = getMoonPhaseAngle(context, currentJulDay)
                
                if (previousPhase > 180.0 && currentPhase < 180.0) {
                    // 精確定位新月時刻
                    return refineNewMoonTime(context, currentJulDay - 0.5, currentJulDay)
                }
                
                previousPhase = currentPhase
            }
            
        } catch (e: Exception) {
            LogUtil.e("尋找下一個新月時發生錯誤: ${e.message}")
        }
        
        return startJulDay + 29.5 // 預設返回一個月後
    }

    /**
     * 尋找下一個滿月
     */
    private fun findNextFullMoon(context: Context, startJulDay: Double): Double {
        try {
            var currentJulDay = startJulDay
            var previousPhase = getMoonPhaseAngle(context, currentJulDay)
            
            // 尋找月相角度接近180度的時刻（滿月）
            for (i in 1..60) { // 最多搜索60天
                currentJulDay += 0.5
                val currentPhase = getMoonPhaseAngle(context, currentJulDay)
                
                if (abs(currentPhase - 180.0) < 5.0) {
                    // 精確定位滿月時刻
                    return refineFullMoonTime(context, currentJulDay - 0.5, currentJulDay)
                }
                
                previousPhase = currentPhase
            }
            
        } catch (e: Exception) {
            LogUtil.e("尋找下一個滿月時發生錯誤: ${e.message}")
        }
        
        return startJulDay + 14.75 // 預設返回半個月後
    }

    /**
     * 獲取月相角度（太陽月亮的角度差）
     */
    private fun getMoonPhaseAngle(context: Context, julDay: Double): Double {
        try {
            val timeMillis = ((julDay - 2451545.0) * 86400000).toLong()
            val sunAngle = EphemerisUtil.getPlanetAngle(context, timeMillis, LatLng(0.0, 0.0), SweConst.SE_SUN, false)
            val moonAngle = EphemerisUtil.getPlanetAngle(context, timeMillis, LatLng(0.0, 0.0), SweConst.SE_MOON, false)
            
            var phase = moonAngle - sunAngle
            if (phase < 0) phase += 360.0
            if (phase >= 360.0) phase -= 360.0
            
            return phase
            
        } catch (e: Exception) {
            LogUtil.e("獲取月相角度時發生錯誤: ${e.message}")
            return 0.0
        }
    }

    /**
     * 精確定位新月時刻
     */
    private fun refineNewMoonTime(context: Context, startJulDay: Double, endJulDay: Double): Double {
        var start = startJulDay
        var end = endJulDay
        
        // 二分法精確定位
        for (i in 1..10) {
            val mid = (start + end) / 2.0
            val phase = getMoonPhaseAngle(context, mid)
            
            if (phase > 180.0) {
                start = mid
            } else {
                end = mid
            }
        }
        
        return (start + end) / 2.0
    }

    /**
     * 精確定位滿月時刻
     */
    private fun refineFullMoonTime(context: Context, startJulDay: Double, endJulDay: Double): Double {
        var bestTime = startJulDay
        var bestDiff = Double.MAX_VALUE
        
        var currentTime = startJulDay
        while (currentTime <= endJulDay) {
            val phase = getMoonPhaseAngle(context, currentTime)
            val diff = abs(phase - 180.0)
            
            if (diff < bestDiff) {
                bestDiff = diff
                bestTime = currentTime
            }
            
            currentTime += 0.01 // 每0.01天檢查一次
        }
        
        return bestTime
    }

    /**
     * 檢查指定時刻是否有日蝕
     */
    private fun checkSolarEclipseAtTime(context: Context, julDay: Double, location: LatLng): AstrologyEvent? {
        try {
            val phase = getMoonPhaseAngle(context, julDay)
            
            // 新月條件：月相角度接近0度
            if (phase <= 15.0 || phase >= 345.0) {
                // 檢查月亮是否接近黃白交點
                val nodeDistance = getDistanceToLunarNode(context, julDay)
                
                if (nodeDistance <= 18.0) { // 日蝕可能發生的最大角度
                    val magnitude = calculateSolarEclipseMagnitude(nodeDistance)
                    val eclipseType = determineSolarEclipseType(magnitude)
                    
                    return AstrologyEventFactory.createEclipseEvent(
                        date = Date(((julDay - 2451545.0) * 86400000).toLong()),
                        eclipseType = eclipseType,
                        magnitude = magnitude
                    )
                }
            }
            
        } catch (e: Exception) {
            LogUtil.e("檢查日蝕時發生錯誤: ${e.message}")
        }
        
        return null
    }

    /**
     * 檢查指定時刻是否有月蝕
     */
    private fun checkLunarEclipseAtTime(context: Context, julDay: Double, location: LatLng): AstrologyEvent? {
        try {
            val phase = getMoonPhaseAngle(context, julDay)
            
            // 滿月條件：月相角度接近180度
            if (abs(phase - 180.0) <= 15.0) {
                // 檢查月亮是否接近黃白交點
                val nodeDistance = getDistanceToLunarNode(context, julDay)
                
                if (nodeDistance <= 12.0) { // 月蝕可能發生的最大角度
                    val magnitude = calculateLunarEclipseMagnitude(nodeDistance)
                    val eclipseType = determineLunarEclipseType(magnitude)
                    
                    return AstrologyEventFactory.createEclipseEvent(
                        date = Date(((julDay - 2451545.0) * 86400000).toLong()),
                        eclipseType = eclipseType,
                        magnitude = magnitude
                    )
                }
            }
            
        } catch (e: Exception) {
            LogUtil.e("檢查月蝕時發生錯誤: ${e.message}")
        }
        
        return null
    }

    /**
     * 檢查新月時的日蝕
     */
    private fun checkSolarEclipseAtNewMoon(context: Context, julDay: Double, location: LatLng): AstrologyEvent? {
        return checkSolarEclipseAtTime(context, julDay, location)
    }

    /**
     * 檢查滿月時的月蝕
     */
    private fun checkLunarEclipseAtFullMoon(context: Context, julDay: Double, location: LatLng): AstrologyEvent? {
        return checkLunarEclipseAtTime(context, julDay, location)
    }

    /**
     * 計算月亮到黃白交點的距離
     */
    private fun getDistanceToLunarNode(context: Context, julDay: Double): Double {
        try {
            val timeMillis = ((julDay - 2451545.0) * 86400000).toLong()
            val moonAngle = EphemerisUtil.getPlanetAngle(context, timeMillis, LatLng(0.0, 0.0), SweConst.SE_MOON, false)
            val nodeAngle = EphemerisUtil.getPlanetAngle(context, timeMillis, LatLng(0.0, 0.0), SweConst.SE_TRUE_NODE, false)
            
            val distance = abs(moonAngle - nodeAngle)
            return min(distance, 360.0 - distance)
            
        } catch (e: Exception) {
            LogUtil.e("計算月亮到交點距離時發生錯誤: ${e.message}")
            return 180.0 // 返回最大距離作為預設值
        }
    }

    /**
     * 計算日蝕食分
     */
    private fun calculateSolarEclipseMagnitude(nodeDistance: Double): Double {
        return max(0.0, (18.0 - nodeDistance) / 18.0)
    }

    /**
     * 計算月蝕食分
     */
    private fun calculateLunarEclipseMagnitude(nodeDistance: Double): Double {
        return max(0.0, (12.0 - nodeDistance) / 12.0)
    }

    /**
     * 確定日蝕類型
     */
    private fun determineSolarEclipseType(magnitude: Double): EclipseType {
        return when {
            magnitude >= 0.9 -> EclipseType.TOTAL_SOLAR
            magnitude >= 0.6 -> EclipseType.ANNULAR_SOLAR
            magnitude >= 0.2 -> EclipseType.PARTIAL_SOLAR
            else -> EclipseType.SOLAR_ECLIPSE
        }
    }

    /**
     * 確定月蝕類型
     */
    private fun determineLunarEclipseType(magnitude: Double): EclipseType {
        return when {
            magnitude >= 0.8 -> EclipseType.TOTAL_LUNAR
            magnitude >= 0.4 -> EclipseType.PARTIAL_LUNAR
            magnitude >= 0.1 -> EclipseType.PENUMBRAL_LUNAR
            else -> EclipseType.LUNAR_ECLIPSE
        }
    }
}
