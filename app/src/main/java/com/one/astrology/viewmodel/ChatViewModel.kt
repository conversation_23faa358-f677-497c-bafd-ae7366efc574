package com.one.astrology.viewmodel

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.LatLng
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.one.astrology.ObjectBox
import com.one.astrology.api.groq.GroqApiService
import com.one.astrology.api.repository.ChatRepository
import com.one.astrology.data.AIModelResponse
import com.one.astrology.data.Horoscope
import com.one.astrology.data.PlanetPosition
import com.one.astrology.data.ReadingData
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.SignBean
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.data.entity.ReadingDataEntity
import com.one.astrology.data.request.groq.GroqRequest
import com.one.astrology.data.request.groq.GroqResponse
import com.one.astrology.data.request.groq.Message
import com.one.astrology.data.request.openai.ChatRequest
import com.one.astrology.data.type.Chart
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.fragment.analysis.calculator.SolarArcCalculator
import com.one.astrology.ui.fragment.analysis.calculator.SolarArcEvent
import com.one.astrology.util.AssetsToObjectUtil
import com.one.astrology.util.CalendarUtil
import com.one.astrology.util.EncryptedSPUtil
import com.one.astrology.util.EphemerisUtil
import com.one.astrology.util.FirdariaCalculator
import com.one.astrology.util.Util
import com.one.core.util.FormatUtils
import com.one.core.util.LogUtil
import kotlinx.coroutines.launch
import java.util.Calendar
import kotlin.math.ceil
import kotlin.math.roundToInt

class ChatViewModel : ViewModel() {
    private val repository = ChatRepository()

    private val _isLoading = MutableLiveData(false)
    val isLoading: LiveData<Boolean> = _isLoading

    private val _data = MutableLiveData<ReadingData>()
    val data: LiveData<ReadingData> = _data

    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    var matchEvent = MutableLiveData<MatchEvent>()

    var readingData = ReadingData("", "", "")

    fun init() {
        _data.value = ReadingData("", "", "")
        _error.value = ""
    }

    private fun saveReadingData() {
        val readingDataBox = ObjectBox.get().boxFor(ReadingDataEntity::class.java)
        val entity = ReadingDataEntity().apply {
            title = readingData.title
            content = readingData.content
            chartInfo = readingData.chartInfo
            chart = readingData.chart
            birthDataA = readingData.birthDataA
            birthDataB = readingData.birthDataB
        }
        readingDataBox.put(entity)
    }

    fun setErrorMessage(message: String) {
        _error.value = message
    }

    /**
     * gpt-4-turbo 便宜、效能高
     * gpt-4 比 gpt-4-turbo 更貴但效果類似
     * gpt-3.5-turbo 便宜但能力較弱
     *模型名稱	每 100 萬個輸入 token 費用	每 100 萬個輸出 token 費用
     * GPT-3.5 Turbo	$1.50	$2.00
     * GPT-4	        $30	    $60
     * GPT-4o	        $5	    $15
     * GPT-4o Mini	    $0.15	$0.60
     * o1-preview	    $15	    $60
     * o1-mini	        $3	    $12
     */
    fun fetchGPTResponse(
        context: Context,
        horoscope: Horoscope,
        type: String,
        prompt: String,
        model: String,
        apiKey: String
    ) {
        _isLoading.value = true
        val isForceUpdate = EncryptedSPUtil.getForcedUpdate(context)
        val firebaseUser = FirebaseAuth.getInstance().currentUser
        checkIfDocumentExists(
            isForceUpdate, firebaseUser, horoscope, type, model,
            { document ->
                // 資料已存在，處理現有資料
                val existingData = document.toObject(AIModelResponse::class.java)
                existingData?.let {
                    LogUtil.d("Existing data found: ${it.response}")
                    readingData.title = type
                    readingData.content = it.response
                    _data.value = readingData
                    saveReadingData()
                    _isLoading.value = false
                }
            },
            {
                // 資料不存在，發送 API 請求
                val request = ChatRequest(
                    model = model,
                    messages = listOf(
                        Message("system", "你是一個專業的占星師"),
                        Message("user", prompt)
                    )
                )
                viewModelScope.launch {
                    val chatResponse = repository.fetchChatResponse(apiKey, request)
                    if (chatResponse != null) {
                        chatResponse.choices.firstOrNull()?.message?.content?.let {
                            if (firebaseUser != null) {
                                val countTokens =
                                    chatResponse.usage.total_tokens // Util.countTokens(it)
                                LogUtil.d("Tokens 數:${countTokens}")
                                val astrologyReading = AIModelResponse(
//                                        horoscope.name,
//                                        horoscope.getBirthdayString(),
//                                        horoscope.getBirthLatLng(),
                                    response = it,
                                    tokens = countTokens,
                                    //                        timestamp = FieldValue.serverTimestamp()
                                )

                                writeAiResponseFirebaseFirestore(
                                    firebaseUser.uid,
                                    horoscope,
                                    model,
                                    type,
                                    astrologyReading
                                )
                            }
                            LogUtil.d("response\n$it")
                            readingData.title = type
                            readingData.content = it
                            _data.value = readingData
                            saveReadingData()
                            _isLoading.value = false
                        }
                    } else {
                        _error.value = "請求失敗"
                        _isLoading.value = false
                    }
                }
            },
            { exception ->
                LogUtil.e("Error checking document: ${exception.message}")
                _isLoading.value = false
            }
        )
    }

    fun fetchGPTResponse(
        context: Context,
        horoscopeA: Horoscope,
        horoscopeB: Horoscope,
        type: String,
        prompt: String,
        model: String,
        apiKey: String
    ) {
        _isLoading.value = true
        val isForceUpdate = EncryptedSPUtil.getForcedUpdate(context)
        val firebaseUser = FirebaseAuth.getInstance().currentUser
        checkIfDocumentExists(
            isForceUpdate, firebaseUser, horoscopeA, horoscopeB, type, model,
            { document ->
                // 資料已存在，處理現有資料
                val existingData = document.toObject(AIModelResponse::class.java)
                existingData?.let {
                    LogUtil.d("Existing data found: ${it.response}")
                    readingData.title = type
                    readingData.content = it.response
                    _data.value = readingData
                    saveReadingData()
                    _isLoading.value = false
                }
            },
            {
                // 資料不存在，發送 API 請求
                val request = ChatRequest(
                    model = model,
                    messages = listOf(
                        Message("system", "你是一個專業的占星師"),
                        Message("user", prompt)
                    )
                )
                viewModelScope.launch {
                    val chatResponse = repository.fetchChatResponse(apiKey, request)
                    if (chatResponse != null) {
                        chatResponse.choices.firstOrNull()?.message?.content?.let {
                            if (firebaseUser != null) {
                                val countTokens =
                                    chatResponse.usage.total_tokens // Util.countTokens(it)
                                LogUtil.d("Tokens 數:${countTokens}")
                                val astrologyReading = AIModelResponse(
                                    response = it,
                                    tokens = countTokens,
                                    //                        timestamp = FieldValue.serverTimestamp()
                                )

                                writeAiResponseFirebaseFirestore(
                                    firebaseUser.uid,
                                    horoscopeA,
                                    horoscopeB,
                                    model,
                                    type,
                                    astrologyReading
                                )
                            }
                            LogUtil.d("response\n$it")
                            readingData.title = type
                            readingData.content = it
                            _data.value = readingData
                            saveReadingData()
                            _isLoading.value = false
                        }
                    } else {
                        _error.value = "請求失敗"
                        _isLoading.value = false
                    }
                }
            },
            { exception ->
                LogUtil.e("Error checking document: ${exception.message}")
                _isLoading.value = false
            }
        )
    }

    /**
     * https://console.groq.com/docs/models
     *
     * Production Models
     * MODEL ID	                    DEVELOPER	CONTEXT WINDOW (TOKENS)	MAX COMPLETION TOKENS	MAX FILE SIZE	MODEL CARD LINK
     * distil-whisper-large-v3-en	HuggingFace	-	-	25 MB
     * gemma2-9b-it	                Google	    8,192	-	-
     * llama-3.3-70b-versatile	    Meta	    128k	32,768	-
     * llama-3.1-8b-instant	Meta	128k	    8,192	-
     * llama-guard-3-8b	            Meta	    8,192	-	-
     * llama3-70b-8192	            Meta	    8,192	-	-
     * llama3-8b-8192	            Meta	    8,192	-	-
     * mixtral-8x7b-32768	        Mistral 	32,768	-	-
     * whisper-large-v3	            OpenAI	    -	-	25 MB
     * whisper-large-v3-turbo	    OpenAI	    -	-	25 MB
     *
     * Preview Models
     * deepseek-r1-distill-llama-70b-specdec	DeepSeek	128k	16,384	-
     * deepseek-r1-distill-llama-70b	        DeepSeek	128k	-	-
     * llama-3.3-70b-specdec	                Meta	    8,192	-	-
     * llama-3.2-1b-preview	                    Meta	    128k	8,192	-
     * llama-3.2-3b-preview	                    Meta	    128k	8,192	-
     * llama-3.2-11b-vision-preview	            Meta	    128k	8,192	-
     * llama-3.2-90b-vision-preview	            Meta	    128k	8,192	-
     */
    fun fetchGroqResponse(
        isForceUpdate: Boolean? = false,
        horoscope: Horoscope,
        type: String,
        prompt: String,
        model: String,
        apiKey: String
    ) {
        _isLoading.value = true
        val firebaseUser = FirebaseAuth.getInstance().currentUser

        if (isForceUpdate != null) {
            checkIfDocumentExists(
                isForceUpdate, firebaseUser, horoscope, type, model,
                { document ->
                    // 資料已存在，處理現有資料
                    val existingData = document.toObject(AIModelResponse::class.java)
                    existingData?.let {
                        LogUtil.d("Existing data found: ${it.response}")
                        readingData.title = type
                        readingData.content = it.response
                        _data.value = readingData
                        saveReadingData()
                        _isLoading.value = false
                    }
                },
                {
                    // 資料不存在，發送 API 請求
                    requestGroqResponse(model, prompt, apiKey, { response ->
                        val reply = response.choices.firstOrNull()?.message?.content.toString()
                        firebaseUser?.let {
                            writeAiResponseToFirestore(it.uid, horoscope, model, type, reply)
                        }
                        readingData.title = type
                        readingData.content = reply
                        _data.value = readingData
                        saveReadingData()
                        LogUtil.d("Groq AI 回應：$response")
                        _isLoading.value = false
                    }, { error ->
                        LogUtil.e("請求失敗：$error")
                        _isLoading.value = false
                        _error.postValue(error.toString())
                    })
                },
                { exception ->
                    LogUtil.e("Error checking document: ${exception.message}")
                    _isLoading.value = false
                }
            )
        }
    }

    fun fetchGroqResponse(
        isForceUpdate: Boolean? = false,
        horoscopeA: Horoscope,
        horoscopeB: Horoscope,
        type: String,
        prompt: String,
        model: String,
        apiKey: String
    ) {
        _isLoading.value = true
        val firebaseUser = FirebaseAuth.getInstance().currentUser

        if (isForceUpdate != null) {
            checkIfDocumentExists(
                isForceUpdate, firebaseUser, horoscopeA, horoscopeB, type, model,
                { document ->
                    // 資料已存在，處理現有資料
                    val existingData = document.toObject(AIModelResponse::class.java)
                    existingData?.let {
                        LogUtil.d("Existing data found: ${it.response}")
                        readingData.title = type
                        readingData.content = it.response
                        _data.value = readingData
                        saveReadingData()
                        _isLoading.value = false
                    }
                },
                {
                    // 資料不存在，發送 API 請求
                    requestGroqResponse(model, prompt, apiKey, { response ->
                        val reply = response.choices.firstOrNull()?.message?.content.toString()
                        firebaseUser?.let {
                            writeAiResponseToFirestore(
                                it.uid,
                                horoscopeA,
                                horoscopeB,
                                model,
                                type,
                                reply
                            )
                        }
                        readingData.title = type
                        readingData.content = reply
                        _data.value = readingData
                        saveReadingData()
                        LogUtil.d("Groq AI 回應：$response")
                        _isLoading.value = false
                    }, { error ->
                        LogUtil.e("請求失敗：$error")
                        _isLoading.value = false
                        _error.postValue(error.toString())
                    })
                },
                { exception ->
                    LogUtil.e("Error checking document: ${exception.message}")
                    _isLoading.value = false
                }
            )
        }
    }

    private fun writeAiResponseFirebaseFirestore(
        userId: String,
        horoscope: Horoscope,
        model: String,
        type: String,
        astrologyReading: AIModelResponse,
    ) {
        val db = FirebaseFirestore.getInstance()
        val doc =
            db.collection("astrology_readings")
                .document(userId)
                .collection(model)
                .document(horoscope.name)
                .collection(horoscope.getBirthdayString())
                .document(type)
        doc.set(astrologyReading)
            .addOnSuccessListener {
                LogUtil.d("DocumentSnapshot successfully written!")
            }
            .addOnFailureListener {
                LogUtil.e("Error writing document : " + it.message)
            }
    }

    private fun writeAiResponseFirebaseFirestore(
        userId: String,
        horoscopeA: Horoscope,
        horoscopeB: Horoscope,
        model: String,
        type: String,
        astrologyReading: AIModelResponse,
    ) {
        val db = FirebaseFirestore.getInstance()
        val doc =
            db.collection("astrology_readings")
                .document(userId)
                .collection(model)
                .document(horoscopeA.name + " vs " + horoscopeB.name)
                .collection(horoscopeA.getBirthdayString() + " vs " + horoscopeB.getBirthdayString())
                .document(type)
        doc.set(astrologyReading)
            .addOnSuccessListener {
                LogUtil.d("DocumentSnapshot successfully written!")
            }
            .addOnFailureListener {
                LogUtil.e("Error writing document : " + it.message)
            }
    }

    fun getBirthData(): BirthData {
        val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
        var birthDataList = birthDataBox.query(BirthData_.isSelected.equal(true)).build().find()
        if (birthDataList.isEmpty()) {
            birthDataList = birthDataBox.query()
                .order(BirthData_.id)
                .build()
                .find()
        }
        if (birthDataList.isEmpty()) {
            return BirthData()
        }
        if (birthDataList[0].birthday == 0L) {
            val timestamp =
                birthDataList[0].birthdayString?.let { FormatUtils.stringToTimestamp(it) }
            if (timestamp != null) {
                birthDataList[0].birthday = timestamp
            }
        }
        return birthDataList[0]
    }

    fun getTwoBirthData(): MatchEvent {
        val matchEvent = MatchEvent()
        val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
        var birthDataList = birthDataBox.query(BirthData_.isSelected.equal(true)).build().find()
        if (birthDataList.size < 2) {
            birthDataList = birthDataBox.query()
                .order(BirthData_.id)
                .build()
                .find()
        }
        if (birthDataList.isEmpty()) {
            return matchEvent
        }
        matchEvent.birthDataA = birthDataList[0]
        matchEvent.birthDataB = birthDataList[1]
        this.matchEvent.value = matchEvent
        return matchEvent
    }

    fun firdariaCalculator(birthData: BirthData) {
        val birthDate = birthData.generateBirthdayString()
        val isDayChart = true  // 根據太陽位置決定

        // 計算主要時期
        val majorPeriods = FirdariaCalculator.calculateMajorPeriods(birthDate, isDayChart)
        LogUtil.d("主要時期：")
        majorPeriods.forEach { (planet, period) ->
            LogUtil.d("$planet: ${period.first} - ${period.second}")
        }

        // 計算第一個主要時期的次要時期
        val firstMajorPeriod = majorPeriods.first()
        val subPeriods = FirdariaCalculator.calculateSubPeriods(firstMajorPeriod.second.first, 10)
        LogUtil.d("\n次要時期 (${firstMajorPeriod.first} 主導)：")
        subPeriods.forEach { (planet, period) ->
            LogUtil.d("$planet: ${period.first} - ${period.second}")
        }
    }

    fun calculateHoroscope(context: Context, chart: Chart, birthData: BirthData): Horoscope {
        _isLoading.value = true
        val horoscope = EphemerisUtil.calculate(
            context,
            chart,
            birthData.name,
            birthData.birthday,
            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude)
        )
        _isLoading.value = false
        return horoscope
    }

    fun getComposite(
        context: Context,
        birthDataA: BirthData,
        birthDataB: BirthData
    ): MatchEvent {
        val matchEvent = MatchEvent()
        matchEvent.birthDataA = birthDataA
        matchEvent.birthDataB = birthDataB
        matchEvent.chartType = Chart.Composite
        val horoscopeA = EphemerisUtil.calculate(
            context,
            Chart.Composite,
            birthDataA.name,
            birthDataA.birthday,
            LatLng(birthDataA.birthplaceLatitude, birthDataA.birthplaceLongitude)
        )

        val horoscopeB = EphemerisUtil.calculate(
            context,
            Chart.Composite,
            birthDataB.name,
            birthDataB.birthday,
            LatLng(birthDataB.birthplaceLatitude, birthDataB.birthplaceLongitude)
        )

        val horoscope =
            EphemerisUtil.calculateComposite(context, Chart.Composite, horoscopeA, horoscopeB)

        matchEvent.planetListA = horoscope.planetList
        matchEvent.houses = horoscope.houses
        matchEvent.aspectList = horoscope.aspectList

        matchEvent.horoscopeA = horoscope
        matchEvent.horoscopeB = horoscopeB
        this.matchEvent.value = matchEvent
        return matchEvent
    }

    fun getCompositeSecondary(
        context: Context,
        birthDataA: BirthData,
        birthDataB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {
        val matchEvent = MatchEvent()
        matchEvent.chartType = Chart.CompositeSecondaryProgression
        matchEvent.birthDataA = birthDataA
        matchEvent.birthDataB = birthDataB
        val horoscopeSecondaryA = getSecondary(
            context,
            birthDataA.name,
            birthDataA.birthday,
            LatLng(birthDataA.birthplaceLatitude, birthDataA.birthplaceLongitude),
            progressedTime
        )

        val horoscopeSecondaryB = getSecondary(
            context,
            birthDataB.name,
            birthDataB.birthday,
            LatLng(birthDataB.birthplaceLatitude, birthDataB.birthplaceLongitude),
            progressedTime
        )

        val horoscope =
            EphemerisUtil.calculateComposite(
                context,
                Chart.CompositeSecondaryProgression,
                horoscopeSecondaryA,
                horoscopeSecondaryB
            )

        matchEvent.planetListA = horoscope.planetList
        matchEvent.houses = horoscope.houses
        matchEvent.aspectList = horoscope.aspectList

        matchEvent.horoscopeA = horoscope
        matchEvent.horoscopeB = horoscope
        this.matchEvent.value = matchEvent
        return matchEvent
    }

    fun getCompositeTertiary(
        context: Context,
        birthDataA: BirthData,
        birthDataB: BirthData
    ): MatchEvent {
        val matchEvent = MatchEvent()
        matchEvent.chartType = Chart.CompositeTertiaryProgression
        matchEvent.birthDataA = birthDataA
        matchEvent.birthDataB = birthDataB
        val horoscopeSecondaryA = getTertiary(
            context,
            birthDataA.name,
            birthDataA.birthday,
            LatLng(birthDataA.birthplaceLatitude, birthDataA.birthplaceLongitude),
            birthDataA.isDaylightSavingTime
        )

        val horoscopeSecondaryB = getTertiary(
            context,
            birthDataB.name,
            birthDataB.birthday,
            LatLng(birthDataB.birthplaceLatitude, birthDataB.birthplaceLongitude),
            birthDataB.isDaylightSavingTime
        )

        val horoscope =
            EphemerisUtil.calculateComposite(
                context,
                Chart.CompositeTertiaryProgression,
                horoscopeSecondaryA,
                horoscopeSecondaryB
            )

        matchEvent.planetListA = horoscope.planetList
        matchEvent.houses = horoscope.houses
        matchEvent.aspectList = horoscope.aspectList

        matchEvent.horoscopeA = horoscope
        matchEvent.horoscopeB = horoscope

        return matchEvent
    }

    private fun getSecondary(
        context: Context,
        name: String,
        time: Long,
        latLng: LatLng,
        progressedTime: BirthData
    ): Horoscope {

        val calendar = Calendar.getInstance()
        calendar.timeInMillis = progressedTime.birthday
        val calendarA = Calendar.getInstance()
        calendarA.timeInMillis = time

        val timeInMillis = calendar.timeInMillis - calendarA.timeInMillis
        // 月亮次限推運法 人出生後每日的行運就是代表每年的行運 365.25
        val date = timeInMillis / 1000 / 60 / 60 / 24 / 365.25
        val hour = timeInMillis / 365.25 / 1000 / 60 / 60
        val minute = timeInMillis / 365.25 / 1000 / 60
        val second = timeInMillis / 365.25 / 1000
        LogUtil.d("date $date hour $hour minute $minute second $second")
        calendarA.add(Calendar.DATE, date.toInt())
        return EphemerisUtil.calculate(
            context,
            Chart.SecondaryProgression,
            name,
            calendarA.timeInMillis,
            LatLng(latLng.latitude, latLng.longitude)
        )
    }

    private fun getTertiary(
        context: Context,
        name: String,
        time: Long,
        latLng: LatLng,
        isDaylightSavingTime: Boolean
    ): Horoscope {

        val calendar = Calendar.getInstance()
        val calendarA = Calendar.getInstance()
        calendarA.timeInMillis = time

        val day = CalendarUtil.getDayDiff(calendar.timeInMillis, time)
        val month = (day / 27.321661).roundToInt()
        calendarA.add(Calendar.DATE, month)

        return EphemerisUtil.calculate(
            context,
            Chart.TertiaryProgression,
            name,
            calendarA.timeInMillis,
            LatLng(latLng.latitude, latLng.longitude)
        )
    }

    fun getDavison(
        context: Context,
        birthDataA: BirthData,
        birthDataB: BirthData
    ): MatchEvent {
        val matchEvent = MatchEvent()
        matchEvent.birthDataA = birthDataA
        matchEvent.birthDataB = birthDataB
        matchEvent.chartType = Chart.Davison
        val time = (birthDataA.birthday + birthDataB.birthday) / 2
        val birthplaceLatitude =
            (birthDataA.birthplaceLatitude + birthDataB.birthplaceLatitude) / 2
        val birthplaceLongitude =
            (birthDataA.birthplaceLongitude + birthDataB.birthplaceLongitude) / 2

        matchEvent.horoscopeA = EphemerisUtil.calculate(
            context,
            Chart.Davison,
            birthDataA.name + " vs " + birthDataB.name,
            time,
            LatLng(birthplaceLatitude, birthplaceLongitude)
        )
        matchEvent.planetListA = matchEvent.horoscopeA.planetList
        matchEvent.houses = matchEvent.horoscopeA.houses
        matchEvent.aspectList = matchEvent.horoscopeA.aspectList
        this.matchEvent.value = matchEvent
        return matchEvent
    }

    fun getMarks(
        context: Context,
        birthDataA: BirthData,
        birthDataB: BirthData
    ): MatchEvent {
        val matchEvent = MatchEvent()
        matchEvent.birthDataA = birthDataA
        matchEvent.birthDataB = birthDataB
        matchEvent.chartType = Chart.Marks
        val time = (birthDataA.birthday + birthDataB.birthday) / 2
        val birthplaceLatitude =
            (birthDataA.birthplaceLatitude + birthDataB.birthplaceLatitude) / 2
        val birthplaceLongitude =
            (birthDataA.birthplaceLongitude + birthDataB.birthplaceLongitude) / 2

        val timeA = (birthDataA.birthday + time) / 2
        val latitudeA = (birthDataA.birthplaceLatitude + birthplaceLatitude) / 2
        val longitudeA = (birthDataA.birthplaceLongitude + birthplaceLongitude) / 2

        matchEvent.horoscopeA = EphemerisUtil.calculate(
            context,
            Chart.Marks,
            birthDataA.name,
            timeA,
            LatLng(latitudeA, longitudeA)
        )
        matchEvent.planetListA = matchEvent.horoscopeA.planetList
        matchEvent.houses = matchEvent.horoscopeA.houses
        matchEvent.aspectList = matchEvent.horoscopeA.aspectList
        this.matchEvent.value = matchEvent
        return matchEvent
    }

    fun calculateHoroscope(
        context: Context,
        chart: Chart,
        birthDataA: BirthData,
        birthDataB: BirthData
    ): MatchEvent {
        val matchEvent = MatchEvent()
        matchEvent.birthDataA = birthDataA
        matchEvent.birthDataB = birthDataB
        matchEvent.chartType = chart
        val horoscopeA = EphemerisUtil.calculate(
            context,
            chart,
            birthDataA.name,
            birthDataA.birthday,
            LatLng(birthDataA.birthplaceLatitude, birthDataA.birthplaceLongitude)
        )

        val horoscopeB = EphemerisUtil.calculate(
            context,
            chart,
            birthDataB.name,
            birthDataB.birthday,
            LatLng(birthDataB.birthplaceLatitude, birthDataB.birthplaceLongitude)
        )
        matchEvent.planetListA = getPlanetBeanList(context, horoscopeB, horoscopeA)
        matchEvent.planetListB = getPlanetBeanList(context, horoscopeA, horoscopeB)
        matchEvent.houses = horoscopeA.houses
        matchEvent.aspectList = EphemerisUtil.aspects(
            context,
            chart,
            horoscopeB.planetList,
            horoscopeA.planetList,
            true,
            false
        )

        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        this.matchEvent.value = matchEvent
        return matchEvent
    }

    fun getSecondary(
        context: Context,
        birthData: BirthData,
        progressedTime: BirthData
    ): Horoscope {

        val calendar = Calendar.getInstance()
        calendar.timeInMillis = progressedTime.birthday
        val calendarA = Calendar.getInstance()
        calendarA.timeInMillis = birthData.birthday

        val timeInMillis = calendar.timeInMillis - calendarA.timeInMillis
        // 月亮次限推運法 人出生後每日的行運就是代表每年的行運 365.25
        val date = timeInMillis / 1000 / 60 / 60 / 24 / 365.25
        val hour = timeInMillis / 365.25 / 1000 / 60 / 60
        val minute = timeInMillis / 365.25 / 1000 / 60
        val second = timeInMillis / 365.25 / 1000
        LogUtil.d("date $date hour $hour minute $minute second $second")
        calendarA.add(Calendar.DATE, date.toInt())
        return EphemerisUtil.calculate(
            context,
            Chart.SecondaryProgression,
            birthData.name,
            calendarA.timeInMillis,
            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude)
        )
    }

    private fun calculateSolarArc(birthDateMillis: Long, currentTimeMillis: Long): Double {
        // 使用 Calendar 來設定出生日期和當前日期
        val calendar = Calendar.getInstance().apply {
            timeInMillis = currentTimeMillis
        }
        val calendarA = Calendar.getInstance().apply {
            timeInMillis = birthDateMillis
        }

        // 計算兩個日期之間的天數差距
        val daysBetween = (calendar.timeInMillis - calendarA.timeInMillis) / (1000 * 60 * 60 * 24)

        // 將天數轉換為年數 (365.25是考慮閏年的平均天數)
        val years = daysBetween / 365.0

        // 計算 Solar Arc (太陽每年大約移動1度)
        val solarArc = ceil(years) // 1度每年

        return solarArc
    }

    fun getSolarArc(context: Context, birthData: BirthData, time: Long): MatchEvent {
        val matchEvent = MatchEvent()
        matchEvent.birthDataA = birthData
        matchEvent.chartType = Chart.SolarArc

        val solarArc = calculateSolarArc(birthData.birthday, time)

        val horoscopeA = EphemerisUtil.calculate(
            context,
            Chart.SolarArc,
            birthData.name,
            birthData.birthday,
            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude)
        )

        matchEvent.birthDataB = birthData

        horoscopeA.getPlanetBeanList().forEach {
            it.longitude = (it.longitude + solarArc) % 360
        }

        matchEvent.planetListA = getPlanetBeanList(context, horoscopeA, horoscopeA)
        matchEvent.houses = horoscopeA.houses
        matchEvent.aspectList = EphemerisUtil.aspects(
            context,
            Chart.SolarArc,
            horoscopeA.planetList,
            horoscopeA.planetList,
            true,
            false
        )
        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeA

        this.matchEvent.value = matchEvent
        return matchEvent
    }

    private fun solarArcCalculator(
        age: Int,
        planetList: ArrayList<PlanetBean>
    ): List<SolarArcEvent> {
        val natalChart = planetList.map { PlanetPosition(it.chName, it.longitude) }
        val solarArcCalculator = SolarArcCalculator(natalChart)
        val events = solarArcCalculator.detectAspects(age)

        LogUtil.d("=== $age 歲推運影響 ===")
        var string = "\n"
        events.forEach {
            string += "${it.age} 歲 ${it.planetArc}  ${it.aspect}  ${it.planetNatal}\n"
        }
        LogUtil.d(string)
        return events
    }

    fun solarArcCalculator(planetList: ArrayList<PlanetBean>): List<SolarArcEvent> {
        val calculatedEvents: ArrayList<SolarArcEvent> = ArrayList()
        for (i in 0 until 100) {
            val event = solarArcCalculator(i, planetList)
            calculatedEvents.addAll(event)
        }
        return calculatedEvents
    }

    fun getSolarReturn(
        context: Context,
        birthData: BirthData,
        progressedTime: BirthData
    ): MatchEvent {
        val matchEvent = MatchEvent()
        matchEvent.birthDataA = birthData
        matchEvent.chartType = Chart.SolarReturn
        var residenceLatitude = birthData.residenceLatitude
        if (residenceLatitude == null) {
            residenceLatitude = birthData.birthplaceLatitude
        }
        var residenceLongitude = birthData.residenceLongitude
        if (residenceLongitude == null) {
            residenceLongitude = birthData.birthplaceLongitude
        }
        val horoscope = EphemerisUtil.solarReturn(
            context,
            birthData.name,
            birthData.birthday,
            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude),
            progressedTime,
            LatLng(residenceLatitude, residenceLongitude),
            birthData.isDaylightSavingTime
        )

        matchEvent.planetListA = getPlanetBeanList(context, horoscope, horoscope)
        matchEvent.houses = horoscope.houses
        matchEvent.aspectList = EphemerisUtil.aspects(
            context,
            Chart.SolarReturn,
            horoscope.planetList,
            horoscope.planetList,
            false,
            false
        )
        horoscope.birthdayTime = birthData.birthday
        matchEvent.horoscopeA = horoscope
        matchEvent.horoscopeB = horoscope
        return matchEvent
    }

    fun getLunarReturn(
        context: Context,
        birthData: BirthData,
        progressedTime: BirthData
    ): MatchEvent {
        val matchEvent = MatchEvent()
        matchEvent.birthDataA = birthData
        matchEvent.chartType = Chart.LunarReturn
        var residenceLatitude = birthData.residenceLatitude
        if (residenceLatitude == null) {
            residenceLatitude = birthData.birthplaceLatitude
        }
        var residenceLongitude = birthData.residenceLongitude
        if (residenceLongitude == null) {
            residenceLongitude = birthData.birthplaceLongitude
        }
        val horoscope = EphemerisUtil.lunarReturn(
            context,
            birthData.name,
            birthData.birthday,
            LatLng(residenceLatitude, residenceLongitude),
            progressedTime,
            birthData.isDaylightSavingTime
        )
        matchEvent.planetListA = getPlanetBeanList(context, horoscope, horoscope)
        matchEvent.houses = horoscope.houses
        matchEvent.aspectList = EphemerisUtil.aspects(
            context,
            Chart.SolarReturn,
            horoscope.planetList,
            horoscope.planetList,
            false,
            false
        )
        matchEvent.horoscopeA = horoscope
        matchEvent.horoscopeB = horoscope
        return matchEvent
    }

    fun getPlanetBeanList(
        context: Context,
        horoscopeA: Horoscope,
        horoscopeB: Horoscope
    ): ArrayList<PlanetBean> {
        val signList = AssetsToObjectUtil.getSignList(context)
        val planetBeanList =
            horoscopeA.getPlanetBeanList().sortedWith(compareBy {
                it.id
            })
        val planetList = planetBeanList.filter { it.isChecked }
        val list = ArrayList<PlanetBean>()
        for (planet in planetList) {
            if (planet.longitude < 0) {
                LogUtil.e("行星度數異常(小於0) ${planet.chName} ${planet.longitude}°")
                continue
            }
            val strings: ArrayList<String> = EphemerisUtil.szZodiac(planet.longitude)
            planet.signBean = SignBean(signList[strings[0].toInt()])
            planet.signBean.degree = "${strings[1]}°"
            planet.signBean.minute = strings[2]
            planet.signBean.houseData =
                EphemerisUtil.house(planet.longitude, horoscopeB.houses.cusps)
            LogUtil.i("星位 ${planet.chName} ${planet.signBean.chName} ${strings[1]}°${strings[2]} ${planet.signBean.houseData.index}宮")
            list.add(planet)
        }
        return list
    }


    private fun checkIfDocumentExists(
        isForceUpdate: Boolean = false,
        firebaseUser: FirebaseUser?,
        horoscope: Horoscope,
        type: String,
        model: String,
        onDocumentFound: (DocumentSnapshot) -> Unit,  // 當資料存在時的處理
        onDocumentNotFound: () -> Unit,                // 當資料不存在時的處理
        onFailure: (Exception) -> Unit                 // 當查詢失敗時的處理
    ) {
        if (isForceUpdate) {
            onDocumentNotFound()
            return
        }
        if (firebaseUser == null) {
            onDocumentNotFound()
            return
        }
        firebaseUser.let {
            val db = FirebaseFirestore.getInstance()
            val docCollection = db.collection("astrology_readings")
                .document(it.uid)
                .collection(model)
                .document(horoscope.name)
                .collection(horoscope.getBirthdayString())
                .document(type)
            docCollection.get()
                .addOnSuccessListener { document ->
                    if (document.exists()) {
                        onDocumentFound(document)  // 傳回現有資料
                    } else {
                        onDocumentNotFound()  // 資料不存在時調用
                    }
                }
                .addOnFailureListener { exception ->
                    onFailure(exception)  // 查詢失敗時調用
                }
        }
    }

    private fun checkIfDocumentExists(
        isForceUpdate: Boolean = false,
        firebaseUser: FirebaseUser?,
        horoscopeA: Horoscope,
        horoscopeB: Horoscope,
        type: String,
        model: String,
        onDocumentFound: (DocumentSnapshot) -> Unit,  // 當資料存在時的處理
        onDocumentNotFound: () -> Unit,                // 當資料不存在時的處理
        onFailure: (Exception) -> Unit                 // 當查詢失敗時的處理
    ) {
        if (isForceUpdate) {
            onDocumentNotFound()
            return
        }
        firebaseUser?.let {
            val db = FirebaseFirestore.getInstance()
            val docCollection = db.collection("astrology_readings")
                .document(it.uid)
                .collection(model)
                .document(horoscopeA.name + " vs " + horoscopeB.name)
                .collection(horoscopeA.getBirthdayString() + " vs " + horoscopeB.getBirthdayString())
                .document(type)
            docCollection.get()
                .addOnSuccessListener { document ->
                    if (document.exists()) {
                        onDocumentFound(document)  // 傳回現有資料
                    } else {
                        onDocumentNotFound()  // 資料不存在時調用
                    }
                }
                .addOnFailureListener { exception ->
                    onFailure(exception)  // 查詢失敗時調用
                }
        }
    }

    private fun requestGroqResponse(
        model: String,
        prompt: String,
        apiKey: String,
        onSuccess: (GroqResponse) -> Unit,
        onFailure: (String?) -> Unit
    ) {
        val service = GroqApiService.create(apiKey)
        val request = GroqRequest(
            model = model,
            messages = listOf(Message("user", prompt))
        )

        service.getChatCompletion(request).enqueue(object : retrofit2.Callback<GroqResponse> {
            override fun onResponse(
                call: retrofit2.Call<GroqResponse>,
                response: retrofit2.Response<GroqResponse>
            ) {
                if (response.isSuccessful) {
                    response.body()?.let {
                        onSuccess(it)
                    } ?: onFailure(response.message())
                } else {
                    onFailure(response.errorBody()?.string())
                }
            }

            override fun onFailure(call: retrofit2.Call<GroqResponse>, t: Throwable) {
                onFailure(t.message)
            }
        })
    }

    private fun writeAiResponseToFirestore(
        firebaseUserUid: String,
        horoscope: Horoscope,
        model: String,
        type: String,
        response: String
    ) {
        val countTokens = Util.countTokens(response)
        LogUtil.d("Tokens 數:$countTokens")

        val aiModelResponse = AIModelResponse(
//            horoscope.name,
//            horoscope.getBirthdayString(),
//            horoscope.getBirthLatLng(),
            response = response,
            tokens = countTokens,
//            timestamp = FieldValue.serverTimestamp()
        )

        writeAiResponseFirebaseFirestore(firebaseUserUid, horoscope, model, type, aiModelResponse)
    }


    private fun writeAiResponseToFirestore(
        firebaseUserUid: String,
        horoscopeA: Horoscope,
        horoscopeB: Horoscope,
        model: String,
        type: String,
        response: String
    ) {
        val countTokens = Util.countTokens(response)
        LogUtil.d("Tokens 數:$countTokens")

        val aiModelResponse = AIModelResponse(
//            horoscopeA.name,
//            horoscopeA.getBirthdayString(),
//            horoscopeA.getBirthLatLng(),
            response = response,
            tokens = countTokens,
//            timestamp = FieldValue.serverTimestamp()
        )

        writeAiResponseFirebaseFirestore(
            firebaseUserUid,
            horoscopeA,
            horoscopeB,
            model,
            type,
            aiModelResponse
        )
    }
}
