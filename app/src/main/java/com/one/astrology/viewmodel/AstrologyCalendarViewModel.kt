package com.one.astrology.viewmodel

import android.app.Activity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.data.model.DailyAstrologyInfo
import com.one.astrology.data.type.Chart
import com.one.astrology.util.EncryptedSPUtil
import com.one.astrology.util.EphemerisUtil
import com.one.astrology.util.LocationUtil
import com.one.core.util.FormatUtils
import com.one.core.util.LogUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import swisseph.SweConst
import java.util.Calendar
import java.util.Date
import javax.inject.Inject

/**
 * 星象日曆 ViewModel
 */
@HiltViewModel
class AstrologyCalendarViewModel @Inject constructor() : ViewModel() {

    private val _currentMonth = MutableStateFlow(Date())
    val currentMonth: StateFlow<Date> = _currentMonth

    private val _selectedDate = MutableStateFlow<Date?>(Date())
    val selectedDate: StateFlow<Date?> = _selectedDate

    private val _dailyAstrologyInfo = MutableStateFlow<DailyAstrologyInfo?>(null)
    val dailyAstrologyInfo: StateFlow<DailyAstrologyInfo?> = _dailyAstrologyInfo

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading

    init {
        // 預設選擇今天
        selectDate(Date())
    }

    /**
     * 選擇日期
     */
    fun selectDate(date: Date) {
        _selectedDate.value = date
        // 當選擇日期時，清空之前的星象數據
        _dailyAstrologyInfo.value = null
    }

    /**
     * 載入選中日期的星象數據
     */
    fun loadAstrologyDataForSelectedDate(activity: Activity) {
        val date = _selectedDate.value ?: return

        viewModelScope.launch {
            try {
                _isLoading.value = true

                // 獲取預設地點（可以從設定中獲取或使用台北作為預設）
                val defaultLocation = getDefaultLocation(activity)

                val astrologyInfo = calculateDailyAstrologyInfo(activity, date, defaultLocation)
                _dailyAstrologyInfo.value = astrologyInfo

            } catch (e: Exception) {
                LogUtil.e("載入星象數據時發生錯誤: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 上個月
     */
    fun previousMonth() {
        val calendar = Calendar.getInstance()
        calendar.time = _currentMonth.value
        calendar.add(Calendar.MONTH, -1)
        _currentMonth.value = calendar.time
    }

    /**
     * 下個月
     */
    fun nextMonth() {
        val calendar = Calendar.getInstance()
        calendar.time = _currentMonth.value
        calendar.add(Calendar.MONTH, 1)
        _currentMonth.value = calendar.time
    }

    /**
     * 獲取預設地點
     */
    private fun getDefaultLocation(activity: Activity): LatLng {
        // 嘗試從每日星象設定中獲取地點
        val savedLocation = EncryptedSPUtil.getSavedDailyAstrologyLocation(activity)
        if (savedLocation != null) {
            return savedLocation.first
        }

        // 如果沒有儲存的地點，使用台北作為預設
        return LatLng(25.0330, 121.5654) // 台北市
    }

    /**
     * 計算指定日期的星象信息
     */
    private suspend fun calculateDailyAstrologyInfo(
        activity: Activity,
        date: Date,
        location: LatLng
    ): DailyAstrologyInfo {
        val dateStr = FormatUtils.dateToString(date, "yyyy-MM-dd")

        // 設定時間為當天中午12點
        val calendar = Calendar.getInstance()
        calendar.time = date
        calendar.set(Calendar.HOUR_OF_DAY, 12)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)

        val noonTime = calendar.time

        try {
            // 計算星盤
            val horoscope = EphemerisUtil.calculate(
                activity,
                Chart.Celestial,
                activity.getString(Chart.Celestial.type),
                noonTime.time,
                location
            )

            // 獲取行星位置
            val planetList = horoscope.getPlanetBeanList()
                .filter { it.isClassic || it.id == SweConst.SE_URANUS || it.id == SweConst.SE_NEPTUNE || it.id == SweConst.SE_PLUTO }

            // 計算重要相位
            val aspects = calculateImportantAspects(planetList)

            // 獲取地點名稱
            val address = try {
                LocationUtil.latLngToAddresses(location)
            } catch (e: Exception) {
                "未知地點"
            }

            val addressParts = address.split(",")
            val area = if (addressParts.size > 1) {
                addressParts[1].trim()
            } else {
                address
            }

            return DailyAstrologyInfo(
                date = dateStr,
                planets = planetList,
                importantAspects = aspects,
                address = area,
                hasPermissions = true
            )

        } catch (e: Exception) {
            LogUtil.e("計算星象數據時發生錯誤: ${e.message}")

            // 返回空的星象信息
            return DailyAstrologyInfo(
                date = dateStr,
                planets = emptyList(),
                importantAspects = emptyList(),
                address = "計算錯誤",
                hasPermissions = false
            )
        }
    }

    /**
     * 計算重要相位
     */
    private fun calculateImportantAspects(planetList: List<com.one.astrology.data.bean.PlanetBean>): List<String> {
        val aspects = mutableListOf<String>()

        try {
            // 簡化的相位計算，只檢查主要行星之間的重要相位
            val mainPlanets = planetList.filter {
                it.id in listOf(
                    SweConst.SE_SUN, SweConst.SE_MOON, SweConst.SE_MERCURY,
                    SweConst.SE_VENUS, SweConst.SE_MARS, SweConst.SE_JUPITER,
                    SweConst.SE_SATURN
                )
            }

            for (i in mainPlanets.indices) {
                for (j in i + 1 until mainPlanets.size) {
                    val planet1 = mainPlanets[i]
                    val planet2 = mainPlanets[j]

                    val angle = Math.abs(planet1.longitude - planet2.longitude)
                    val normalizedAngle = if (angle > 180) 360 - angle else angle

                    // 檢查主要相位（容許度±5度）
                    when {
                        Math.abs(normalizedAngle - 0) <= 5 -> {
                            aspects.add("${planet1.chName} 合 ${planet2.chName}")
                        }
                        Math.abs(normalizedAngle - 60) <= 5 -> {
                            aspects.add("${planet1.chName} 六分 ${planet2.chName}")
                        }
                        Math.abs(normalizedAngle - 90) <= 5 -> {
                            aspects.add("${planet1.chName} 四分 ${planet2.chName}")
                        }
                        Math.abs(normalizedAngle - 120) <= 5 -> {
                            aspects.add("${planet1.chName} 三分 ${planet2.chName}")
                        }
                        Math.abs(normalizedAngle - 180) <= 5 -> {
                            aspects.add("${planet1.chName} 對分 ${planet2.chName}")
                        }
                    }
                }
            }

        } catch (e: Exception) {
            LogUtil.e("計算相位時發生錯誤: ${e.message}")
        }

        return aspects.take(5) // 只返回前5個重要相位
    }
}
