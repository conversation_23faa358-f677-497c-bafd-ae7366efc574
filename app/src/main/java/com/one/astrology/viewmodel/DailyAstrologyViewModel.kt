package com.one.astrology.viewmodel

import android.app.Activity
import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.model.DailyAstrologyInfo
import com.one.astrology.data.model.PlanetDailyStatus
import com.one.astrology.data.type.Chart
import com.one.astrology.util.AssetsToObjectUtil
import com.one.astrology.util.EncryptedSPUtil
import com.one.astrology.util.EphemerisUtil
import com.one.astrology.util.EphemerisUtil.Companion.getJulDay
import com.one.astrology.util.EphemerisUtil.Companion.initData
import com.one.astrology.util.LocationUtil
import com.one.astrology.util.PreferencesResetHelper
import com.one.core.util.LogUtil
import com.one.core.util.formatDate
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import swisseph.SweConst
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.ObjectInputStream
import java.io.ObjectOutputStream
import java.util.Calendar
import java.util.TimeZone
import javax.crypto.AEADBadTagException
import javax.inject.Inject

/**
 * 每日星象ViewModel
 * 負責獲取和計算當天的行星位置和相位
 */
@HiltViewModel
class DailyAstrologyViewModel @Inject constructor(
    application: Application
) : AndroidViewModel(application) {

    private val _dailyAstrologyInfo = MutableStateFlow<DailyAstrologyInfo?>(null)
    val dailyAstrologyInfo: StateFlow<DailyAstrologyInfo?> = _dailyAstrologyInfo.asStateFlow()

    val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val CACHE_FILE_NAME = "daily_astrology_cache.dat"

    init {
        // 在ViewModel初始化時嘗試從緩存中讀取數據
        viewModelScope.launch {
            val context = getApplication<Application>()
            val cachedInfo = loadDailyAstrologyInfoFromCache(context)
            if (cachedInfo != null) {
                _dailyAstrologyInfo.value = cachedInfo
                LogUtil.d("已從緩存中讀取星象數據")
            }
        }
    }

    /**
     * 儲存星象數據到檔案
     */
    private fun saveDailyAstrologyInfoToCache(context: Context, info: DailyAstrologyInfo) {
        try {
            val cacheDir = context.cacheDir
            val cacheFile = File(cacheDir, CACHE_FILE_NAME)

            // 使用ObjectOutputStream直接將對象序列化到檔案
            ObjectOutputStream(FileOutputStream(cacheFile)).use { oos ->
                oos.writeObject(info)
            }

            LogUtil.d("已將星象數據儲存到檔案: ${cacheFile.absolutePath}")
        } catch (e: Exception) {
            LogUtil.e("儲存星象數據到檔案時發生錯誤: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 從檔案讀取星象數據
     */
    private fun loadDailyAstrologyInfoFromCache(context: Context): DailyAstrologyInfo? {
        try {
            val cacheDir = context.cacheDir
            val cacheFile = File(cacheDir, CACHE_FILE_NAME)

            if (!cacheFile.exists()) {
                LogUtil.d("緩存檔案不存在: ${cacheFile.absolutePath}")
                return null
            }

            // 使用ObjectInputStream直接從檔案反序列化對象
            val info = ObjectInputStream(FileInputStream(cacheFile)).use { ois ->
                ois.readObject() as DailyAstrologyInfo
            }

            LogUtil.d("已從檔案讀取星象數據: ${cacheFile.absolutePath}")
            return info
        } catch (e: Exception) {
            LogUtil.e("從檔案讀取星象數據時發生錯誤: ${e.message}")
            e.printStackTrace()

            // 如果讀取失敗，嘗試刪除緩存檔案
            try {
                val cacheDir = context.cacheDir
                val cacheFile = File(cacheDir, CACHE_FILE_NAME)
                if (cacheFile.exists()) {
                    cacheFile.delete()
                    LogUtil.d("刪除損壞的緩存檔案: ${cacheFile.absolutePath}")
                }
            } catch (ex: Exception) {
                LogUtil.e("刪除緩存檔案時發生錯誤: ${ex.message}")
            }

            return null
        }
    }

    /**
     * 加載今日星象數據
     */
    fun loadTodayAstrologyData(
        activity: Activity,
        latLng: LatLng,
        forceUpdate: Boolean = false
    ) {
        viewModelScope.launch {
            // 開始時先設置為載入中，確保用戶知道正在處理
            _isLoading.value = true

            try {
                // 獲取今天的日期字符串
                val today = Calendar.getInstance().timeInMillis.formatDate("yyyy-MM-dd")

                // 獲取上次更新的日期
                val lastUpdate = EncryptedSPUtil.getDailyAstrologyLastUpdate(activity)

                // 如果不是強制更新且上次更新的日期是今天
                if (!forceUpdate && lastUpdate == today) {
                    // 嘗試從緩存中讀取數據
                    val cachedInfo = loadDailyAstrologyInfoFromCache(activity)

                    if (cachedInfo != null) {
                        LogUtil.d("使用緩存的今日星象數據，最後更新日期: $lastUpdate")
                        _dailyAstrologyInfo.value = cachedInfo
                        _isLoading.value = false  // 確保載入狀態設置為false
                        return@launch
                    }
                }

                // 計算今日星象信息
                val todayInfo = calculateTodayAstrologyInfo(activity, latLng)

                try {
                    // 獲取地點名稱
                    val cityName = LocationUtil.latLngToAddresses(latLng, listOf("city"))
                    todayInfo.address = cityName

                    // 儲存地點到偏好設定
                    EncryptedSPUtil.saveDailyAstrologyLocation(activity, latLng, cityName)
                } catch (e: Exception) {
                    LogUtil.e("獲取地點名稱時發生錯誤: ${e.message}")
                    // 如果獲取地點名稱失敗，使用預設值
                    todayInfo.address = "未知地點"
                }

                todayInfo.hasPermissions = true
                _dailyAstrologyInfo.value = todayInfo

                // 儲存星象數據到檔案
                saveDailyAstrologyInfoToCache(activity, todayInfo)

                // 儲存更新日期
                EncryptedSPUtil.saveDailyAstrologyLastUpdate(activity, today)

                _isLoading.value = false
                LogUtil.d("已更新今日星象數據，日期: $today")
            } catch (e: Exception) {
                LogUtil.e("計算今日星象數據時發生錯誤: ${e.message}")
                _isLoading.value = false

                // 如果發生錯誤但已有緩存數據，則使用緩存數據
                if (_dailyAstrologyInfo.value == null) {
                    val cachedInfo = loadDailyAstrologyInfoFromCache(activity)
                    if (cachedInfo != null) {
                        LogUtil.d("發生錯誤，使用緩存的今日星象數據")
                        _dailyAstrologyInfo.value = cachedInfo
                    }
                }
            }
        }
    }

    /**
     * 計算今天的星象信息
     */
    private suspend fun calculateTodayAstrologyInfo(
        context: Context,
        location: LatLng
    ): DailyAstrologyInfo {

        val calendar = Calendar.getInstance()
        val today = calendar.timeInMillis

        // 獲取當前日期
        val dateStr = today.formatDate("yyyy/MM/dd HH:mm")

        // 獲取今日月相
//        val moonPhase = MoonPhaseUtil.getMoonPhaseFromSwe(context, calendar, location)

        // 處理可能的加密錯誤
        val horoscope = try {
            EphemerisUtil.calculate(
                context,
                Chart.Celestial,
                context.getString(Chart.Celestial.type),
                today,
                location
            )
        } catch (e: Exception) {
            // 檢查是否為加密錯誤
            if (e.cause is AEADBadTagException || e.toString().contains("AEADBadTagException")) {
                LogUtil.e("Encryption error detected: ${e.message}")
                // 在主線程顯示重置對話框
                (context as? Activity)?.runOnUiThread {
                    PreferencesResetHelper.showResetDialog(context)
                }
            } else {
                LogUtil.e("Error calculating horoscope: ${e.message}")
            }

            // 創建一個空的星盤作為備用
            com.one.astrology.data.Horoscope("Today", today, location)
        }

        // 計算行星位置
        val planetList = horoscope.getPlanetBeanList()
            .filter { it.isClassic || it.id == SweConst.SE_URANUS || it.id == SweConst.SE_NEPTUNE || it.id == SweConst.SE_PLUTO } //calculateTodayPlanetPositions(context, location)

        // 獲取重要相位
        val aspects = calculateImportantAspects(planetList)

        val address = LocationUtil.latLngToAddresses(LatLng(location.latitude, location.longitude))
        val addressParts = address.split(",")
        var area = ""
        if (addressParts.size > 1) {
            area = addressParts[1].trim()
        } else {
            LogUtil.e("地址格式異常：$address")
        }

        return DailyAstrologyInfo(
            date = dateStr,
//            moonPhase = moonPhase,
            planets = planetList,
            importantAspects = aspects,
            address = area,
            hasPermissions = true
        )
    }

    /**
     * 計算今天的行星位置
     */
    private fun calculateTodayPlanetPositions(
        context: Context,
        location: LatLng
    ): List<PlanetDailyStatus> {
        val currentTime = Calendar.getInstance().timeInMillis

        // 獲取行星列表
        val planetList = AssetsToObjectUtil.getPlanetList(context)

        // 篩選主要行星
        val mainPlanets = planetList.filter {
            it.id == SweConst.SE_SUN ||
                    it.id == SweConst.SE_MOON ||
                    it.id == SweConst.SE_MERCURY ||
                    it.id == SweConst.SE_VENUS ||
                    it.id == SweConst.SE_MARS ||
                    it.id == SweConst.SE_JUPITER ||
                    it.id == SweConst.SE_SATURN ||
                    it.id == SweConst.SE_URANUS ||
                    it.id == SweConst.SE_NEPTUNE ||
                    it.id == SweConst.SE_PLUTO
        }

        // 計算每個行星的位置
        val result = mutableListOf<PlanetDailyStatus>()

        mainPlanets.forEach { planet ->
            // 獲取行星角度
            val angle = EphemerisUtil.getPlanetAngle(
                context,
                currentTime,
                location,
                planet.id,
                false
            )

            // 獲取星座
            val signIndex = (angle / 30).toInt()
            val signList = AssetsToObjectUtil.getSignList(context)
            val sign = signList[signIndex]

            // 計算度數
            val degree = angle % 30

            // 判斷是否逆行
            val isRetrograde = EphemerisUtil.isPlanetRetrograde(context, currentTime, planet.id)

            result.add(
                PlanetDailyStatus(
                    id = planet.id,
                    name = planet.chName,
                    symbol = planet.symbol,
                    color = planet.color,
                    signName = sign.chName,
                    degree = Math.round(degree * 100.0) / 100.0,
                    isRetrograde = isRetrograde
                )
            )
        }

        return result
    }

    /**
     * 計算今天的重要相位
     */
    private fun calculateImportantAspects(planets: List<PlanetBean>): List<String> {
        val aspects = mutableListOf<String>()

        // 尋找重要相位 (僅考慮主要行星之間的合相、對相、三分相)
        for (i in planets.indices) {
            for (j in i + 1 until planets.size) {
                val planet1 = planets[i]
                val planet2 = planets[j]

                // 計算兩個行星之間的角度差
                val angle1 = planet1.longitude + (planet1.signBean.chName.getSignIndex() * 30)
                val angle2 = planet2.longitude + (planet2.signBean.chName.getSignIndex() * 30)

                var diff = Math.abs(angle1 - angle2)
                if (diff > 180) diff = 360 - diff

                // 判斷相位類型（設置允許誤差為3度）
                when {
                    isDegreeInRange(diff, 0.0, 3.0) -> {
                        aspects.add("${planet1.chName} 合 ${planet2.chName}")
                    }

                    isDegreeInRange(diff, 60.0, 3.0) -> {
                        aspects.add("${planet1.chName} 六合 ${planet2.chName}")
                    }

                    isDegreeInRange(diff, 90.0, 3.0) -> {
                        aspects.add("${planet1.chName} 刑 ${planet2.chName}")
                    }

                    isDegreeInRange(diff, 120.0, 3.0) -> {
                        aspects.add("${planet1.chName} 拱 ${planet2.chName}")
                    }

                    isDegreeInRange(diff, 180.0, 3.0) -> {
                        aspects.add("${planet1.chName} 衝 ${planet2.chName}")
                    }
                }
            }
        }

        // 只返回最重要的幾個相位（例如：最多5個）
        return aspects.take(5)
    }

    /**
     * 檢查角度是否在指定範圍內
     */
    private fun isDegreeInRange(degree: Double, target: Double, orb: Double): Boolean {
        return degree >= target - orb && degree <= target + orb
    }

    /**
     * 獲取星座索引
     */
    private fun String.getSignIndex(): Int {
        return when (this) {
            "白羊座" -> 0
            "金牛座" -> 1
            "雙子座" -> 2
            "巨蟹座" -> 3
            "獅子座" -> 4
            "處女座" -> 5
            "天秤座" -> 6
            "天蠍座" -> 7
            "射手座" -> 8
            "摩羯座" -> 9
            "水瓶座" -> 10
            "雙魚座" -> 11
            else -> 0
        }
    }
}

/**
 * 月相工具類
 */
object MoonPhaseUtil {
    fun getMoonPhaseName(calendar: Calendar): String {
        // 獲取月齡（0-29.53天）
        val moonAge = calculateMoonAge(calendar)

        return when {
            moonAge < 1.84 -> "新月"
            moonAge < 5.53 -> "峨眉月"
            moonAge < 9.22 -> "上弦月"
            moonAge < 12.91 -> "盈凸月"
            moonAge < 16.61 -> "滿月"
            moonAge < 20.30 -> "虧凸月"
            moonAge < 23.99 -> "下弦月"
            moonAge < 27.68 -> "殘月"
            else -> "新月前夕"
        }
    }

    /**
     * 計算月齡（0-29.53天）
     */
//    private fun calculateMoonAge(calendar: Calendar): Double {
//        // 從1900年1月15日的新月開始計算
//        val newMoon = Calendar.getInstance().apply {
//            set(1900, 0, 15, 0, 0, 0)
//            set(Calendar.MILLISECOND, 0)
//        }
//
//        // 月相周期為29.53天
//        val lunarCycle = 29.53
//
//        // 計算經過的天數
//        val diffMillis = calendar.timeInMillis - newMoon.timeInMillis
//        val diffDays = diffMillis / (24 * 60 * 60 * 1000.0)
//
//        // 計算月齡
//        return diffDays % lunarCycle
//    }

    fun getMoonPhaseFromSwe(context: Context, calendar: Calendar, location: LatLng): String {
        initData(context)
        val julDay = getJulDay(calendar.timeInMillis, location)
        val year = calendar[Calendar.YEAR]
        val month = calendar[Calendar.MONTH] + 1
        val day = calendar[Calendar.DAY_OF_MONTH]
        val hour = calendar[Calendar.HOUR_OF_DAY]
        val minute = calendar[Calendar.MINUTE]
        LogUtil.d("time: $year/$month/$day $hour:$minute  ${location.latitude}, ${location.longitude}")
        val moonLongitude = EphemerisUtil.getSweLongitude(julDay, SweConst.SE_MOON)
        val sunLongitude = EphemerisUtil.getSweLongitude(julDay, SweConst.SE_SUN)
        val phaseAngle = ((sunLongitude - moonLongitude + 360.0) % 360.0)

        return when {
            phaseAngle < 22.5 -> "新月"
            phaseAngle < 67.5 -> "峨眉月"
            phaseAngle < 112.5 -> "上弦月"
            phaseAngle < 157.5 -> "盈凸月"
            phaseAngle < 202.5 -> "滿月"
            phaseAngle < 247.5 -> "虧凸月"
            phaseAngle < 292.5 -> "下弦月"
            phaseAngle < 337.5 -> "殘月"
            else -> "新月前夕"
        }
    }

    private fun calculateMoonAge(calendar: Calendar): Double {
        val baseNewMoon = Calendar.getInstance(TimeZone.getTimeZone("UTC")).apply {
            set(1900, 0, 6, 18, 14, 0) // 接近1900年精確新月時間
            set(Calendar.MILLISECOND, 0)
        }

        val lunarCycle = 29.530588853 // 更精確的月相周期
        val diffMillis = calendar.timeInMillis - baseNewMoon.timeInMillis
        val diffDays = diffMillis / (24 * 60 * 60 * 1000.0)

        val moonAge = diffDays % lunarCycle
        return if (moonAge < 0) moonAge + lunarCycle else moonAge // 確保為正值
    }

}