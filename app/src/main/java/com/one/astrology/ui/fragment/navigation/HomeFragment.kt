package com.one.astrology.ui.fragment.navigation

import android.Manifest
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CalendarMonth
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.LatLng
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.constant.KeyDefine
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.data.type.Chart
import com.one.astrology.data.type.ChartType
import com.one.astrology.event.AddSignRecordEvent
import com.one.astrology.event.EventKey
import com.one.astrology.firebase.RemoteConfigManager
import com.one.astrology.ui.activity.AnnouncementDetailActivity
import com.one.astrology.ui.activity.SignDetailActivity
import com.one.astrology.ui.compose.components.AnnouncementBanner
import com.one.astrology.ui.compose.components.BirthDataCard
import com.one.astrology.ui.compose.components.ChartTypeItem
import com.one.astrology.ui.compose.components.DailyAstrologyCard
import com.one.astrology.ui.compose.dialog.LocationPickerDialog
import com.one.astrology.ui.fragment.BaseFragment
import com.one.astrology.ui.fragment.bottomSheet.SelectBottomSheetFragment
import com.one.astrology.ui.fragment.bottomSheet.SingleSelectBottomSheetFragment
import com.one.astrology.ui.fragment.dialog.BirthDataDialogFragment
import com.one.astrology.ui.fragment.dialog.CelestialDataFragment
import com.one.astrology.ui.fragment.dialog.UserSelectionDialog
import com.one.astrology.util.AssetsToObjectUtil
import com.one.astrology.util.EncryptedSPUtil
import com.one.astrology.util.LocationUtil
import com.one.astrology.util.astro.ChartUtils.toStorageValue
import com.one.astrology.viewmodel.DailyAstrologyViewModel
import com.one.core.billing.BillingClientLifecycle
import com.one.core.util.FormatUtils
import com.one.core.util.LogUtil
import com.one.core.util.PermissionsUtil
import com.one.core.viewmodel.BillingModelFactory
import com.one.core.viewmodel.BillingViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * HomeFragment
 */
@AndroidEntryPoint
class HomeFragment : BaseFragment(R.layout.fragment_home) {

    private val dailyAstrologyViewModel by viewModels<DailyAstrologyViewModel>()
    private lateinit var billingClientLifecycle: BillingClientLifecycle
    private lateinit var billingViewModel: BillingViewModel
    private var selectedBirthData: BirthData? = null

    @Inject
    lateinit var remoteConfigManager: RemoteConfigManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initBillingClientLifecycle()

        // 監聽新增出生資料事件
        LiveEventBus.get<AddSignRecordEvent>(EventKey.AddUserBirthData).observe(this) { event ->
            // 更新選中的出生資料
            if (selectedBirthData != null) {
                selectedBirthData!!.isSelected = false
                selectedBirthData?.let {
                    ObjectBox.get().boxFor(BirthData::class.java).put(it)
                }
            }

            // 將新的出生資料設為選中
            event.signRecord.isSelected = true
            ObjectBox.get().boxFor(BirthData::class.java).put(event.signRecord)
            selectedBirthData = event.signRecord

            // 重新載入視圖
            view?.let { view ->
                val composeView = view as? ComposeView
                composeView?.setContent {
                    MaterialTheme {
                        Surface(
                            modifier = Modifier.fillMaxSize(),
                            color = MaterialTheme.colorScheme.background
                        ) {
                            HomeScreen()
                        }
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("首頁", this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            // 設置視圖組合策略以正確處理生命週期
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            selectedBirthData = getSelectedBirthData()
            setContent {
                MaterialTheme {
                    Surface(
                        modifier = Modifier.fillMaxSize(),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        // 在 Compose 中處理生命週期
                        LaunchedEffect(Unit) {
                            // 在協程中載入數據，避免阻塞UI線程
                            try {
                                loadTodayAstrologyData()
                            } catch (e: Exception) {
                                LogUtil.e("載入今日星象數據時發生錯誤: ${e.message}")
                            }
                        }
                        HomeScreen()
                    }
                }
            }
        }
    }

    private fun loadTodayAstrologyData(forceUpdate: Boolean = false) {
        try {
            // 嘗試從儲存的偏好設定中獲取地點
            val savedLocation = EncryptedSPUtil.getSavedDailyAstrologyLocation(requireContext())

            if (savedLocation != null) {
                // 使用儲存的地點
                val (latLng, _) = savedLocation
                LogUtil.d("使用儲存的今日星象地點: ${latLng.latitude}, ${latLng.longitude}")
                dailyAstrologyViewModel.loadTodayAstrologyData(requireActivity(), latLng, forceUpdate)
            } else {
                // 如果沒有儲存的地點，則使用選定的出生資料地點
                selectedBirthData?.let {
                    LatLng(it.birthplaceLatitude, it.birthplaceLongitude).let { latLng ->
                        LogUtil.d("使用出生資料地點: ${latLng.latitude}, ${latLng.longitude}")
                        dailyAstrologyViewModel.loadTodayAstrologyData(requireActivity(), latLng, forceUpdate)
                    }
                }
            }
        } catch (e: Exception) {
            LogUtil.e("載入今日星象數據時發生錯誤: ${e.message}")
            // 確保即使發生錯誤，也不會一直顯示載入中
            dailyAstrologyViewModel.viewModelScope.launch {
                dailyAstrologyViewModel._isLoading.value = false
            }
        }
    }

    @Composable
    private fun HomeScreen() {
        val announcementList by remoteConfigManager.announcement.collectAsState(initial = emptyList())
        val dailyAstrologyInfo by dailyAstrologyViewModel.dailyAstrologyInfo.collectAsState(initial = null)
        val isLoading by dailyAstrologyViewModel.isLoading.collectAsState(initial = false)
        val chartTypes = AssetsToObjectUtil.getChartType(requireContext())

        // 地點選擇對話框相關狀態
        var showLocationDialog by remember { mutableStateOf(false) }
        var isLocationLoading by remember { mutableStateOf(false) }
        var showBirthDataRequiredDialog by remember { mutableStateOf(false) }
        val coroutineScope = rememberCoroutineScope()

        var isShowDialog by remember { mutableStateOf(false) }
        if (isShowDialog) {
            UserSelectionDialog(
                onDismissRequest = { isShowDialog = false },
                isMultiSelect = false,
                onUserSelected = { birthDataList ->
                    if (birthDataList.isEmpty()) {
                        return@UserSelectionDialog
                    }
                    if (selectedBirthData != null) {
                        selectedBirthData!!.isSelected = false
                        selectedBirthData?.let {
                            ObjectBox.get().boxFor(BirthData::class.java).put(
                                it
                            )
                        }
                    }
                    if (birthDataList.isNotEmpty()) {
                        birthDataList[0].isSelected = true
                        ObjectBox.get().boxFor(BirthData::class.java).put(birthDataList[0])
                    }
                    selectedBirthData = birthDataList[0]
                },
                parentFragmentManager = requireActivity().supportFragmentManager
            )
        }

        // 顯示出生資料必選對話框
        if (showBirthDataRequiredDialog) {
            BirthDataRequiredDialog(
                onDismiss = { showBirthDataRequiredDialog = false },
                onConfirm = {
                    showBirthDataRequiredDialog = false
                    // 創建一個新的出生資料對象
                    val newBirthData = BirthData()
                    // 顯示BirthDataDialogFragment來新增出生資料
                    val dialogFragment = BirthDataDialogFragment(newBirthData)

                    // 設置對話框關閉後的回調
                    dialogFragment.setOnDismissListener { savedBirthData ->
                        if (savedBirthData != null) {
                            // 發送新增出生資料事件
                            LiveEventBus.get<AddSignRecordEvent>(EventKey.AddUserBirthData)
                                .post(AddSignRecordEvent(savedBirthData))
                        }
                    }

                    dialogFragment.show(requireActivity().supportFragmentManager, "")
                }
            )
        }

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = 8.dp)
        ) {
            // Announcements
            items(announcementList.filter { it.isVisible }) { announcement ->
                AnnouncementBanner(
                    announcement = announcement,
                    onAnnouncementClick = {
                        val intent =
                            Intent(requireContext(), AnnouncementDetailActivity::class.java)
                        intent.putExtra("announcement", it)
                        startActivity(intent)
                    }
                )
            }

            // Birth Data Card
            item {
                BirthDataCard(
                    birthData = selectedBirthData,
                    onItemClick = {
                        selectedBirthData?.let { data ->
                            val bundle = Bundle().apply {
                                putParcelable(KeyDefine.UserBirthDataA, data)
                                putString(KeyDefine.Chart, Chart.Natal.toStorageValue())
                            }
                            startActivity(SignDetailActivity::class.java, bundle)
                        } ?: run {
                            // 顯示提示對話框
                            showBirthDataRequiredDialog = true
                        }
                    },
                    onSelectClick = {
                        isShowDialog = true
                    }
                )
            }

            // Quick Access Section - 快捷功能區域
            if (selectedBirthData != null) {
                item {
                    QuickAccessSection(
                        onChartTypeClick = { chartType ->
                            toChart(chartType)
                        },
                        onCalendarClick = {
                            // TODO: 導航到星象日曆
                            Toast.makeText(requireContext(), "星象日曆功能開發中...", Toast.LENGTH_SHORT).show()
                        }
                    )
                }
            }

            if (selectedBirthData != null) {
                // Daily Astrology
                item {
                    if (isLoading) {
                        // 只在真正載入時顯示載入指示器
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                color = colorResource(id = R.color.colorPrimary)
                            )
                        }
                    } else if (dailyAstrologyInfo != null) {
                        // 有數據時顯示卡片
                        DailyAstrologyCard(
                            dailyInfo = dailyAstrologyInfo!!,
                            onClick = {
                                selectedBirthData!!.name = "每日星象"
                                selectedBirthData!!.birthday =
                                    FormatUtils.stringToTimestamp(dailyAstrologyInfo!!.date.trim())
                                val bundle = Bundle()
                                bundle.putString(KeyDefine.Chart, Chart.Celestial.toStorageValue())
                                bundle.putParcelable(KeyDefine.UserBirthData, selectedBirthData)
                                startActivity(SignDetailActivity::class.java, bundle)
                            },
                            onLocationClick = {
                                // 顯示地點選擇對話框
                                showLocationDialog = true
                            },
                            onRefreshClick = {
                                // 強制更新今日星象數據
                                loadTodayAstrologyData(forceUpdate = true)
                                Toast.makeText(requireContext(), "正在更新星象數據...", Toast.LENGTH_SHORT).show()
                            }
                        )

                        // 地點選擇對話框
                        if (showLocationDialog) {
                            LocationPickerDialog(
                                currentLocation = dailyAstrologyInfo!!.address,
                                onDismiss = { showLocationDialog = false },
                                onConfirm = { newLocation ->
                                    showLocationDialog = false
                                    // 如果地點沒有變化，則不需要更新
                                    if (newLocation != dailyAstrologyInfo!!.address) {
                                        // 在協程中搜索新地點的經緯度
                                        coroutineScope.launch {
                                            try {
                                                isLocationLoading = true
                                                val latLng = LocationUtil.addressToLatLng(requireContext(), newLocation)

                                                // 儲存地點到偏好設定
                                                EncryptedSPUtil.saveDailyAstrologyLocation(requireContext(), latLng, newLocation)

                                                // 載入今日星象數據，強制更新
                                                dailyAstrologyViewModel.loadTodayAstrologyData(
                                                    requireActivity(),
                                                    latLng,
                                                    forceUpdate = true
                                                )

                                                Toast.makeText(requireContext(), "已更新位置: $newLocation", Toast.LENGTH_SHORT).show()
                                                isLocationLoading = false
                                            } catch (e: Exception) {
                                                LogUtil.e("搜索地點時發生錯誤: ${e.message}")
                                                Toast.makeText(requireContext(), "搜索地點失敗，請重試", Toast.LENGTH_SHORT).show()
                                                isLocationLoading = false
                                            }
                                        }
                                    }
                                },
                                onUseCurrentLocation = {
                                    showLocationDialog = false
                                    locationPermissionLauncher.launch(Manifest.permission.ACCESS_FINE_LOCATION)
                                },
                                onSearchLocation = { searchText ->
                                    // 在協程中搜索地點
                                    coroutineScope.launch {
                                        try {
                                            isLocationLoading = true
                                            val latLng = LocationUtil.addressToLatLng(requireContext(), searchText)

                                            // 儲存地點到偏好設定
                                            val cityName = LocationUtil.latLngToAddresses(latLng, listOf("city"))
                                            EncryptedSPUtil.saveDailyAstrologyLocation(requireContext(), latLng, cityName)

                                            // 載入今日星象數據，強制更新
                                            dailyAstrologyViewModel.loadTodayAstrologyData(
                                                requireActivity(),
                                                latLng,
                                                forceUpdate = true
                                            )

                                            Toast.makeText(requireContext(), "已更新位置: $cityName", Toast.LENGTH_SHORT).show()
                                            showLocationDialog = false
                                            isLocationLoading = false
                                        } catch (e: Exception) {
                                            LogUtil.e("搜索地點時發生錯誤: ${e.message}")
                                            Toast.makeText(requireContext(), "搜索地點失敗，請重試", Toast.LENGTH_SHORT).show()
                                            isLocationLoading = false
                                        }
                                    }
                                },
                                isLoading = isLocationLoading
                            )
                        }
                    }
                }
            }


            // Chart Types
            chartTypes.forEach { chartTypeItem ->
                item {
                    ChartTypeSection(chartTypeItem)
                }
            }
        }
    }

    private fun getSelectedBirthData(): BirthData? {
        val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
        val selectedData = birthDataBox.query(BirthData_.isSelected.equal(true)).build().find()
        if (selectedData.isNotEmpty()) {
            return selectedData[0]
        } else {
            val data = birthDataBox.query().order(BirthData_.id).build().findFirst()
            return data
        }
    }

    /**
     * 出生資料必選對話框
     * 當用戶沒有選擇出生資料時顯示此對話框
     */
    @Composable
    private fun BirthDataRequiredDialog(
        onDismiss: () -> Unit,
        onConfirm: () -> Unit
    ) {
        Dialog(onDismissRequest = onDismiss) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                ),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 6.dp
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(20.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 圖標
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .clip(RoundedCornerShape(24.dp))
                            .padding(8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = null,
                            tint = colorResource(id = R.color.colorPrimary),
                            modifier = Modifier.size(32.dp)
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // 標題
                    Text(
                        text = "請先選擇出生資料",
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.sp,
                        color = colorResource(id = R.color.colorPrimary)
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // 內容
                    Text(
                        text = "您需要先新增一個出生資料才能繼續操作",
                        fontSize = 14.sp,
                        color = Color.Gray,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )

                    Spacer(modifier = Modifier.height(20.dp))

                    Divider(color = Color.LightGray.copy(alpha = 0.3f))

                    Spacer(modifier = Modifier.height(20.dp))

                    // 按鈕區域
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // 取消按鈕
                        OutlinedButton(
                            onClick = onDismiss,
                            modifier = Modifier.weight(1f),
                            shape = RoundedCornerShape(12.dp),
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = Color.Gray
                            )
                        ) {
                            Text(
                                text = "取消",
                                fontWeight = FontWeight.Medium
                            )
                        }

                        // 確認按鈕
                        Button(
                            onClick = onConfirm,
                            modifier = Modifier.weight(1f),
                            shape = RoundedCornerShape(12.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = colorResource(id = R.color.colorPrimary)
                            )
                        ) {
                            Text(
                                text = "新增資料",
                                fontWeight = FontWeight.Bold,
                                color = Color.White
                            )
                        }
                    }
                }
            }
        }
    }

    private val locationPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                LocationUtil.getCurrentLocation(requireActivity(), 1001) { location ->
                    val latLng = LatLng(location.latitude, location.longitude)

                    // 在協程中獲取地點名稱和處理後續操作
                    lifecycleScope.launch {
                        try {
                            // 獲取地點名稱
                            val cityName = LocationUtil.latLngToAddresses(latLng, listOf("city"))

                            // 儲存地點到偏好設定
                            EncryptedSPUtil.saveDailyAstrologyLocation(requireContext(), latLng, cityName)

                            // 載入今日星象數據，強制更新
                            dailyAstrologyViewModel.loadTodayAstrologyData(
                                requireActivity(),
                                latLng,
                                forceUpdate = true
                            )

                            // 顯示提示訊息
                            Toast.makeText(requireContext(), "已更新位置: $cityName", Toast.LENGTH_SHORT).show()
                        } catch (e: Exception) {
                            LogUtil.e("獲取地點名稱時發生錯誤: ${e.message}")
                            Toast.makeText(requireContext(), "獲取地點名稱失敗", Toast.LENGTH_SHORT).show()

                            // 即使獲取地點名稱失敗，仍然使用新的地理座標並強制更新
                            dailyAstrologyViewModel.loadTodayAstrologyData(
                                requireActivity(),
                                latLng,
                                forceUpdate = true
                            )
                        }
                    }
                }
            } else {
                LogUtil.d("定位權限被拒絕")
                Toast.makeText(requireContext(), "需要定位權限以更新位置", Toast.LENGTH_SHORT).show()
            }
        }

    @Composable
    private fun ChartTypeSection(chartTypeItem: ChartType.ChartTypeItem) {
        Column {
            chartTypeItem.content
                .filter { it.visible != false }
                .forEach { content ->
                    ChartTypeItem(
                        content = content,
                        onClick = { toChart(content.type) }
                    )
                }
        }
    }

    private fun initBillingClientLifecycle() {
        billingClientLifecycle = BillingClientLifecycle.getInstance(requireContext())
        lifecycle.addObserver(billingClientLifecycle)
        billingViewModel = ViewModelProvider(
            this,
            BillingModelFactory(requireActivity().application, billingClientLifecycle)
        )[BillingViewModel::class.java]

        BillingClientLifecycle.productsIdLiveData.observe(requireActivity()) {
            it.forEach { item ->
                PermissionsUtil.permissions.canLogin =
                    PermissionsUtil.permissions.login?.contains(item) == true
                PermissionsUtil.permissions.canNoAd =
                    PermissionsUtil.permissions.noAd?.contains(item) == true
                PermissionsUtil.permissions.canSaveImage =
                    PermissionsUtil.permissions.saveImage?.contains(item) == true
                PermissionsUtil.permissions.canSavePdf =
                    PermissionsUtil.permissions.savePdf?.contains(item) == true
            }
        }
    }

    private fun toChart(name: String) {
        when (val chart = Chart.valueOf(name)) {
            Chart.Celestial -> {
                CelestialDataFragment().show(requireActivity().supportFragmentManager, "")
            }

            Chart.Natal,
            Chart.Firdaria,
            Chart.Transit,
            Chart.SecondaryProgression,
            Chart.SecondaryProgressionSynastry,
            Chart.TertiaryProgression,
            Chart.TertiaryProgressionSynastry,
            Chart.SolarReturn,
            Chart.LunarReturn,
            Chart.SolarArc -> {
                val onClickListener = View.OnClickListener {
                    val birthDataA = it.tag as BirthData
                    val bundle = Bundle()
                    bundle.putParcelable(KeyDefine.UserBirthDataA, birthDataA)
                    bundle.putString(KeyDefine.Chart, chart.toStorageValue())
                    startActivity(SignDetailActivity::class.java, bundle)
                }
                SingleSelectBottomSheetFragment.newInstance(onClickListener).show(
                    requireActivity().supportFragmentManager,
                    "SelectBottomSheetFragment"
                )
            }

            Chart.Synastry,
            Chart.SynastrySecondaryProgression,
            Chart.SynastryTertiaryProgression,
            Chart.Composite,
            Chart.CompositeSecondaryProgression,
            Chart.CompositeTertiaryProgression,
            Chart.Davison,
            Chart.DavisonSecondaryProgression,
            Chart.DavisonTertiaryProgression,
            Chart.Marks,
            Chart.MarksSecondaryProgression,
            Chart.MarksTertiaryProgression -> {
                val matchCallBack = object : SelectBottomSheetFragment.MatchCallBack {
                    override fun onClick(list: List<BirthData?>) {
                        if (list.size == 2) {
                            val birthDataA = list[0]!!
                            val birthDataB = list[1] ?: return
                            val bundle = Bundle()
                            bundle.putParcelable(KeyDefine.UserBirthDataA, birthDataA)
                            bundle.putParcelable(KeyDefine.UserBirthDataB, birthDataB)
                            bundle.putString(KeyDefine.Chart, chart.toStorageValue())
                            startActivity(SignDetailActivity::class.java, bundle)
                        }
                    }
                }
                SelectBottomSheetFragment.newInstance(matchCallBack).show(
                    requireActivity().supportFragmentManager,
                    "SelectBottomSheetFragment"
                )
            }
        }
    }
}

/**
 * 快捷功能區域組件
 */
@Composable
private fun QuickAccessSection(
    onChartTypeClick: (String) -> Unit,
    onCalendarClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 8.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 標題
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Star,
                    contentDescription = null,
                    tint = colorResource(id = R.color.colorPrimary),
                    modifier = Modifier.size(24.dp)
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = "快捷功能",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = colorResource(id = R.color.colorPrimary)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 星盤類型快速選擇
            Text(
                text = "常用星盤",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = Color.Gray
            )

            Spacer(modifier = Modifier.height(8.dp))

            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                contentPadding = PaddingValues(horizontal = 4.dp)
            ) {
                items(getQuickChartTypes()) { chartType ->
                    QuickChartTypeButton(
                        chartType = chartType,
                        onClick = { onChartTypeClick(chartType.type) }
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 星象日曆按鈕
            Button(
                onClick = onCalendarClick,
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(12.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = colorResource(id = R.color.colorPrimary).copy(alpha = 0.1f),
                    contentColor = colorResource(id = R.color.colorPrimary)
                )
            ) {
                Icon(
                    imageVector = Icons.Default.CalendarMonth,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = "星象日曆",
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * 快速星盤類型按鈕
 */
@Composable
private fun QuickChartTypeButton(
    chartType: QuickChartType,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        modifier = Modifier.height(36.dp),
        shape = RoundedCornerShape(18.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = colorResource(id = R.color.colorPrimary),
            contentColor = Color.White
        ),
        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
    ) {
        Text(
            text = chartType.name,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 快速星盤類型數據類
 */
private data class QuickChartType(
    val name: String,
    val type: String
)

/**
 * 獲取常用的星盤類型
 */
private fun getQuickChartTypes(): List<QuickChartType> {
    return listOf(
        QuickChartType("本命盤", "Natal"),
        QuickChartType("行運盤", "Transit"),
        QuickChartType("比較盤", "Synastry"),
        QuickChartType("組合盤", "Composite"),
        QuickChartType("次限盤", "SecondaryProgression")
    )
}

@Composable
fun CenteredLoadingSpinner() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator(
            modifier = Modifier.size(16.dp),
            color = colorResource(id = R.color.colorPrimary)
        )
    }
}
