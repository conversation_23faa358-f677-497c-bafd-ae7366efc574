package com.one.astrology.ui.fragment.viewmodel.calculate

import android.content.Context
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.data.Horoscope
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.event.MatchEvent
import com.one.astrology.util.CalendarUtil
import com.one.astrology.util.EphemerisUtil
import com.one.core.util.LogUtil
import java.util.Calendar
import kotlin.math.roundToInt

open class ProgressionViewModel : BaseCalculateViewModel() {

    private fun getSecondary(
        context: Context,
        name: String,
        time: Long,
        latLng: LatLng,
        progressedTime: BirthData
    ): Horoscope {
        val calendarA = Calendar.getInstance()
        calendarA.timeInMillis = time

        val timeInMillis = progressedTime.birthday - calendarA.timeInMillis
        // 月亮次限推運法 人出生後每日的行運就是代表每年的行運 365.25
        val date = timeInMillis / 1000 / 60 / 60 / 24 / 365.25
        val hour = timeInMillis / 365.25 / 1000 / 60 / 60
        val minute = timeInMillis / 365.25 / 1000 / 60
        val second = timeInMillis / 365.25 / 1000
        LogUtil.d("date $date hour $hour minute $minute second $second")
        calendarA.add(Calendar.DATE, date.toInt())
        return EphemerisUtil.calculate(
            context,
            Chart.SecondaryProgression,
            name,
            calendarA.timeInMillis,
            latLng
        )
    }

    private fun getTertiary(
        context: Context,
        name: String,
        time: Long,
        latLng: LatLng,
        isDaylightSavingTime: Boolean,
        progressedTime: BirthData
    ): Horoscope {
        val calendarA = Calendar.getInstance()
        calendarA.timeInMillis = time

        val day = CalendarUtil.getDayDiff(progressedTime.birthday, time)
        val month = (day / 27.321661).roundToInt()
        calendarA.add(Calendar.DATE, month)

        return EphemerisUtil.calculate(
            context,
            Chart.TertiaryProgression,
            name,
            calendarA.timeInMillis,
            latLng
        )
    }

    fun getSecondaryProgression(
        context: Context,
        birthData: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val horoscopeA = getSecondary(
            context,
            birthData.name,
            birthData.birthday,
            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude),
            progressedTime
        )
        matchEvent.horoscopeB.birthdayTime = progressedTime.birthday

        val horoscopeB = Horoscope()
        horoscopeB.birthdayTime = progressedTime.birthday

        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        matchEvent.horoscopeA.birthdayTime = birthData.birthday

        return matchEvent
    }

    fun getTertiaryProgression(
        context: Context,
        birthData: BirthData,
        progressedTime: BirthData,
    ): MatchEvent {

        val horoscopeA = getTertiary(
            context,
            birthData.name,
            birthData.birthday,
            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude),
            birthData.isDaylightSavingTime,
            progressedTime
        )

        matchEvent.horoscopeB.birthdayTime = progressedTime.birthday

        val horoscopeB = Horoscope()
        horoscopeB.birthdayTime = progressedTime.birthday

        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        matchEvent.horoscopeA.birthdayTime = birthData.birthday

        return matchEvent
    }

    fun getSecondaryProgressionSynastry(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {
        val horoscopeA = EphemerisUtil.calculate(
            context,
            Chart.SecondaryProgressionSynastry,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude)
        )

        val horoscopeB = getSecondary(
            context,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude),
            progressedTime
        )
        horoscopeB.birthdayTime = progressedTime.birthday
        horoscopeA.aspectList =
            EphemerisUtil.aspects(
                context,
                Chart.SecondaryProgressionSynastry,
                horoscopeB.planetList,
                horoscopeA.planetList,
                true, true
            )
        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        return matchEvent
    }

    fun getTertiaryProgressionSynastry(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {
        val horoscopeA = EphemerisUtil.calculate(
            context,
            Chart.TertiaryProgressionSynastry,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude)
        )

        val horoscopeB = getTertiary(
            context,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude),
            signRecordB.isDaylightSavingTime,
            progressedTime
        )
        horoscopeB.birthdayTime = progressedTime.birthday
        horoscopeA.aspectList =
            EphemerisUtil.aspects(
                context,
                Chart.TertiaryProgressionSynastry,
                horoscopeB.planetList,
                horoscopeA.planetList,
                true, true
            )
        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        return matchEvent
    }

    fun getSynastrySecondary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val horoscopeA = getSecondary(
            context,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude),
            progressedTime
        )

        val horoscopeB = getSecondary(
            context,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude),
            progressedTime
        )
        horoscopeA.birthdayTime = progressedTime.birthday
        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        return matchEvent
    }

    fun getSynastryTertiary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val horoscopeA = getTertiary(
            context,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude),
            signRecordA.isDaylightSavingTime,
            progressedTime
        )

        val horoscopeB = getTertiary(
            context,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude),
            signRecordB.isDaylightSavingTime,
            progressedTime
        )
        horoscopeA.birthdayTime = progressedTime.birthday
        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        return matchEvent
    }

    fun getCompositeSecondary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val horoscopeSecondaryA = getSecondary(
            context,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude),
            progressedTime
        )

        val horoscopeSecondaryB = getSecondary(
            context,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude),
            progressedTime
        )

        val horoscope =
            EphemerisUtil.calculateComposite(
                context,
                Chart.CompositeSecondaryProgression,
                horoscopeSecondaryA,
                horoscopeSecondaryB
            )
        horoscope.birthdayTime = progressedTime.birthday
        matchEvent.horoscopeA = horoscope

        return matchEvent
    }

    fun getCompositeTertiary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val horoscopeSecondaryA = getTertiary(
            context,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude),
            signRecordA.isDaylightSavingTime,
            progressedTime
        )

        val horoscopeSecondaryB = getTertiary(
            context,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude),
            signRecordB.isDaylightSavingTime,
            progressedTime
        )

        val horoscope =
            EphemerisUtil.calculateComposite(
                context,
                Chart.CompositeTertiaryProgression,
                horoscopeSecondaryA,
                horoscopeSecondaryB
            )
        horoscope.birthdayTime = progressedTime.birthday
        matchEvent.horoscopeA = horoscope

        return matchEvent
    }

    fun getDavisonSecondary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val time = (signRecordA.birthday + signRecordB.birthday) / 2
        val birthplaceLatitude =
            (signRecordA.birthplaceLatitude + signRecordB.birthplaceLatitude) / 2
        val birthplaceLongitude =
            (signRecordA.birthplaceLongitude + signRecordB.birthplaceLongitude) / 2

        matchEvent.horoscopeA = getSecondary(
            context,
            signRecordA.name,
            time,
            LatLng(birthplaceLatitude, birthplaceLongitude),
            progressedTime
        )
        matchEvent.horoscopeA.birthdayTime = progressedTime.birthday
        return matchEvent
    }

    fun getDavisonTertiary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val time = (signRecordA.birthday + signRecordB.birthday) / 2
        val birthplaceLatitude =
            (signRecordA.birthplaceLatitude + signRecordB.birthplaceLatitude) / 2
        val birthplaceLongitude =
            (signRecordA.birthplaceLongitude + signRecordB.birthplaceLongitude) / 2


        matchEvent.horoscopeA = getTertiary(
            context,
            signRecordA.name,
            time,
            LatLng(birthplaceLatitude, birthplaceLongitude),
            signRecordA.isDaylightSavingTime,
            progressedTime
        )
        matchEvent.horoscopeA.birthdayTime = progressedTime.birthday
        return matchEvent
    }

    fun getMarksSecondary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val time = (signRecordA.birthday + signRecordB.birthday) / 2
        val birthplaceLatitude =
            (signRecordA.birthplaceLatitude + signRecordB.birthplaceLatitude) / 2
        val birthplaceLongitude =
            (signRecordA.birthplaceLongitude + signRecordB.birthplaceLongitude) / 2

        val timeA = (signRecordA.birthday + time) / 2
        val latitudeA = (signRecordA.birthplaceLatitude + birthplaceLatitude) / 2
        val longitudeA = (signRecordA.birthplaceLongitude + birthplaceLongitude) / 2

        matchEvent.horoscopeA = getSecondary(
            context,
            signRecordA.name,
            timeA,
            LatLng(latitudeA, longitudeA),
            progressedTime
        )
        matchEvent.horoscopeA.birthdayTime = progressedTime.birthday
        return matchEvent
    }

    fun getMarksTertiary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val time = (signRecordA.birthday + signRecordB.birthday) / 2
        val birthplaceLatitude =
            (signRecordA.birthplaceLatitude + signRecordB.birthplaceLatitude) / 2
        val birthplaceLongitude =
            (signRecordA.birthplaceLongitude + signRecordB.birthplaceLongitude) / 2

        val timeA = (signRecordA.birthday + time) / 2
        val latitudeA = (signRecordA.birthplaceLatitude + birthplaceLatitude) / 2
        val longitudeA = (signRecordA.birthplaceLongitude + birthplaceLongitude) / 2

        matchEvent.horoscopeA = getTertiary(
            context,
            signRecordA.name,
            timeA,
            LatLng(latitudeA, longitudeA),
            signRecordA.isDaylightSavingTime,
            progressedTime
        )
        matchEvent.horoscopeA.birthdayTime = progressedTime.birthday
        return matchEvent
    }
}