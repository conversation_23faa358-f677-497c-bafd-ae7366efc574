package com.one.astrology.ui.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.one.astrology.ObjectBox
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.db.ChartData
import com.one.astrology.data.db.SettingAspectData
import com.one.astrology.data.db.SettingAspectData_
import com.one.astrology.db.BasicDBHelper
import com.one.astrology.util.AssetsToObjectUtil
import com.one.astrology.util.EncryptedSPUtil
import com.one.astrology.util.EphemerisUtil
import com.one.core.util.LogUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 設定頁面的ViewModel
 */
@HiltViewModel
class SettingsViewModel @Inject constructor() : ViewModel() {

    // 相位設定
    private val _chartList = MutableStateFlow<List<ChartData>>(emptyList())
    val chartList: StateFlow<List<ChartData>> = _chartList

    private val _aspectList = MutableStateFlow<List<SettingAspectData>>(emptyList())
    val aspectList: StateFlow<List<SettingAspectData>> = _aspectList

    private val _currentChart = MutableStateFlow("")
    val currentChart: StateFlow<String> = _currentChart

    // 行星設定
    private val _planetList = MutableStateFlow<List<PlanetBean>>(emptyList())
    val planetList: StateFlow<List<PlanetBean>> = _planetList

    // 其他設定
    private val _houseSystem = MutableStateFlow(EphemerisUtil.houseSystem)
    val houseSystem: StateFlow<Char> = _houseSystem

    private val _chartAnimation = MutableStateFlow(false)
    val chartAnimation: StateFlow<Boolean> = _chartAnimation

    private val _planetRuler = MutableStateFlow(false)
    val planetRuler: StateFlow<Boolean> = _planetRuler

    /**
     * 加載星盤列表
     */
    fun loadChartList(context: Context, currentChartName: String) {
        viewModelScope.launch {
            val list = BasicDBHelper.queryChartList(context)
            val natal = list.find { it.enName == currentChartName }
            if (natal != null) {
                natal.isSelect = true
            }
            _chartList.value = list
            _currentChart.value = currentChartName
            updateAspectList(currentChartName)
        }
    }

    /**
     * 更新當前選中的星盤
     */
    fun updateSelectedChart(chartName: String) {
        viewModelScope.launch {
            _currentChart.value = chartName
            updateAspectList(chartName)
        }
    }

    /**
     * 更新相位列表
     */
    fun updateAspectList(chartName: String) {
        viewModelScope.launch {
            val itemBox = ObjectBox.get().boxFor(SettingAspectData::class.java)
            val aspects = itemBox.query(
                SettingAspectData_.enName.equal(chartName)
            ).build().find()
            _aspectList.value = aspects
        }
    }

    /**
     * 更新相位的容許度
     */
    fun updateOrb(chartName: String, degree: Int, orb: Int) {
        viewModelScope.launch {
            val itemBox = ObjectBox.get().boxFor(SettingAspectData::class.java)
            val item = itemBox.query(
                SettingAspectData_.enName.equal(chartName).and(
                    SettingAspectData_.degree.equal(degree)
                )
            ).build().findFirst()

            if (item != null) {
                item.degree = degree
                item.orb = orb
                itemBox.put(item)
                updateAspectList(chartName)
            }
        }
    }

    /**
     * 更新相位的啟用狀態
     */
    fun updateAspectStatus(chartName: String, degree: Int, isOpen: Boolean) {
        viewModelScope.launch {
            val itemBox = ObjectBox.get().boxFor(SettingAspectData::class.java)
            val item = itemBox.query(
                SettingAspectData_.enName.equal(chartName).and(
                    SettingAspectData_.degree.equal(degree)
                )
            ).build().findFirst()

            if (item != null) {
                item.degree = degree
                item.isOpen = isOpen
                itemBox.put(item)
                updateAspectList(chartName)
            }
        }
    }

    /**
     * 更新相位設定
     */
    fun updateAspectSetting(data: SettingAspectData) {
        viewModelScope.launch {
            val itemBox = ObjectBox.get().boxFor(SettingAspectData::class.java)
            val item = itemBox.query(
                SettingAspectData_.enName.equal(data.enName!!).and(
                    SettingAspectData_.degree.equal(data.degree!!)
                )
            ).build().findFirst()

            if (item != null) {
                item.degree = data.degree
                item.orb = data.orb
                item.isOpen = data.isOpen
                itemBox.put(item)
                updateAspectList(data.enName!!)
            }
        }
    }

    /**
     * 加載行星列表
     */
    fun loadPlanetList(context: Context) {
        viewModelScope.launch {
            val box = ObjectBox.get().boxFor(PlanetBean::class.java)
            var planets = box.all

            if (planets.isEmpty()) {
                // 如果沒有行星數據，從資產中讀取
                planets = AssetsToObjectUtil.getPlanetList(context)

                // 確保每個行星都有唯一的idL
                planets.forEachIndexed { index, planet ->
                    planet.idL = (index + 1).toLong()
                }

                box.put(planets)
            }

            _planetList.value = planets
        }
    }

    /**
     * 保存行星列表
     */
    fun savePlanetList(planetList: List<PlanetBean>) {
        viewModelScope.launch {
            val box = ObjectBox.get().boxFor(PlanetBean::class.java)

            // 獲取現有的行星列表，以保留idL屬性
            val existingPlanets = box.all

            // 更新現有行星的isChecked屬性
            for (existingPlanet in existingPlanets) {
                val updatedPlanet = planetList.find { it.id == existingPlanet.id }
                if (updatedPlanet != null) {
                    existingPlanet.isChecked = updatedPlanet.isChecked
                    LogUtil.d("Saving planet ${existingPlanet.chName} with isChecked=${existingPlanet.isChecked}")
                }
            }

            // 保存更新後的行星列表
            box.put(existingPlanets)

            // 更新UI
            _planetList.value = existingPlanets
        }
    }

    /**
     * 重置行星列表
     */
    fun resetPlanetList(context: Context) {
        viewModelScope.launch {
            // 從資產中讀取預設的行星列表
            val defaultPlanets = AssetsToObjectUtil.getPlanetList(context)

            // 獲取現有的行星列表，以保留idL屬性
            val box = ObjectBox.get().boxFor(PlanetBean::class.java)
            val existingPlanets = box.all

            // 將現有行星的isChecked屬性重置為預設值
            for (existingPlanet in existingPlanets) {
                val defaultPlanet = defaultPlanets.find { it.id == existingPlanet.id }
                if (defaultPlanet != null) {
                    existingPlanet.isChecked = defaultPlanet.isChecked
                }
            }

            // 保存更新後的行星列表
            box.put(existingPlanets)

            // 更新UI
            _planetList.value = existingPlanets
        }
    }

    /**
     * 加載其他設定
     */
    fun loadOtherSettings(context: Context) {
        viewModelScope.launch {
            _houseSystem.value = EncryptedSPUtil.getHouseSystem(context)
            _chartAnimation.value = EncryptedSPUtil.getChartAnimation(context)
            _planetRuler.value = EncryptedSPUtil.getPlanetRuler(context)
        }
    }

    /**
     * 更新宮位系統
     */
    fun updateHouseSystem(context: Context, houseSystem: Char) {
        viewModelScope.launch {
            _houseSystem.value = houseSystem
            EncryptedSPUtil.setHouseSystem(context, houseSystem)
            EphemerisUtil.houseSystem = houseSystem
        }
    }

    /**
     * 更新星盤動畫設定
     */
    fun updateChartAnimation(context: Context, enabled: Boolean) {
        viewModelScope.launch {
            _chartAnimation.value = enabled
            EncryptedSPUtil.setChartAnimation(context, enabled)
        }
    }

    /**
     * 更新顯示宮主星設定
     */
    fun updatePlanetRuler(context: Context, enabled: Boolean) {
        viewModelScope.launch {
            _planetRuler.value = enabled
            EncryptedSPUtil.setPlanetRuler(context, enabled)
        }
    }

    /**
     * 重置所有設定
     */
    fun resetAllSettings(context: Context) {
        viewModelScope.launch {
            // 重置相位設定
            val aspectList = BasicDBHelper.querySettingAspectList(context)
            val itemBox = ObjectBox.get().boxFor(SettingAspectData::class.java)
            itemBox.removeAll()
            itemBox.put(aspectList)
            updateAspectList(_currentChart.value)

            // 重置行星設定
            resetPlanetList(context)

            // 重置其他設定
            EncryptedSPUtil.setHouseSystem(context, 'P')
            EncryptedSPUtil.setChartAnimation(context, false)
            EncryptedSPUtil.setPlanetRuler(context, false)
            loadOtherSettings(context)
        }
    }
}
