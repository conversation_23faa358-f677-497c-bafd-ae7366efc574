package com.one.astrology.ui.activity

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.afollestad.materialdialogs.MaterialDialog
import com.google.android.material.tabs.TabLayout
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.BuildConfig
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.constant.KeyDefine
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.ActivitySignDetailBinding
import com.one.astrology.event.EventKey
import com.one.astrology.ui.fragment.bottomSheet.SelectBottomSheetFragment
import com.one.astrology.ui.fragment.bottomSheet.SettingBottomSheetFragment
import com.one.astrology.ui.fragment.bottomSheet.SingleSelectBottomSheetFragment
import com.one.astrology.ui.fragment.dialog.BirthDataDialogFragment
import com.one.astrology.ui.fragment.report.AspectFragment
import com.one.astrology.ui.fragment.report.ChartFragment
import com.one.astrology.ui.fragment.report.FeatureFragment
import com.one.astrology.ui.fragment.report.FirdariaFragment
import com.one.astrology.ui.fragment.report.HouseFragment
import com.one.astrology.ui.fragment.report.InterpretationFragment
import com.one.astrology.ui.fragment.report.ProportionFragment
import com.one.astrology.ui.fragment.report.ScoreFragment
import com.one.astrology.ui.fragment.report.SignPositionFragment
import com.one.astrology.ui.fragment.report.judgment.JudgmentFragment
import com.one.astrology.ui.fragment.viewmodel.calculate.CalculateViewModel
import com.one.astrology.ui.view.TabLayoutMediators
import com.one.astrology.util.astro.ChartUtils
import com.one.astrology.util.launchWhenStarted
import com.one.core.activity.BaseActivity
import com.one.core.util.FormatUtils
import com.one.core.util.LogUtil
import com.one.core.view.LoadingDialog
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Calendar
import java.util.Date

@AndroidEntryPoint
class SignDetailActivity : BaseActivity() {

    private var isSystem: Boolean = false
    private lateinit var loadingDialog: LoadingDialog
    private var birthData: BirthData = BirthData(Calendar.getInstance().timeInMillis)
    private var birthDataA: BirthData = BirthData(Calendar.getInstance().timeInMillis)
    private var birthDataB: BirthData = BirthData(Calendar.getInstance().timeInMillis)
    private var progressedTime: BirthData = BirthData(Calendar.getInstance().timeInMillis)
    private var mBundle: Bundle? = null
    private lateinit var binding: ActivitySignDetailBinding
    private val viewModel by viewModels<CalculateViewModel>()
    private var isNavCelestial = false

    companion object {
        var chart: Chart = Chart.Natal
    }

    override fun onDestroy() {
        super.onDestroy()
        chart = Chart.Natal
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivitySignDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 先初始化基本 UI
        initBasicUI()

        getEvent()

        // 使用協程異步加載詳細數據
        lifecycleScope.launch {
            initDetailedData()
        }
    }

    private fun initBasicUI() {
        lifecycleScope.launch {
            withContext(Dispatchers.Main) {
                // 初始化基本 UI 元素
                binding.toolbar.setNavigationOnClickListener {
                    LiveEventBus.get<Boolean>(EventKey.CloseSnackbar).postDelay(true, 0)
                    binding.drawerLayout.open()
                }

                if (isSystem) {
                    binding.toolbar.menu.findItem(R.id.action_menu_edit)?.isVisible = false
                }

                binding.navigationView.setNavigationItemSelectedListener { menuItem ->
                    itemSelect(menuItem)
                    binding.viewPager2.currentItem = 0
                    true
                }

                binding.toolbar.setOnMenuItemClickListener { menuItem ->
                    onMenuItemClick(menuItem)
                }

                binding.bottomSheet.ibSwap.setOnClickListener {
                    val onClickListener = View.OnClickListener {
                        birthDataA = it.tag as BirthData
                    }
                    SingleSelectBottomSheetFragment.newInstance(onClickListener).show(
                        supportFragmentManager, ""
                    )
                }
                if (!BuildConfig.IS_DEV) {
                    binding.navigationView.menu.findItem(R.id.nav_synastry_secondary_progression).isVisible =
                        false
                    binding.navigationView.menu.findItem(R.id.nav_synastry_tertiary_progression).isVisible =
                        false
                }
                initTab()
            }
        }
    }

    private suspend fun initDetailedData() = withContext(Dispatchers.Default) {
        try {
            // 在後台線程處理數據
            mBundle?.let { bundle ->
                chart = ChartUtils.fromStorageValue(bundle.getString(KeyDefine.Chart))
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    birthData = bundle.getParcelable(KeyDefine.UserBirthData, BirthData::class.java)
                        ?: BirthData()
                    birthDataA =
                        bundle.getParcelable(KeyDefine.UserBirthDataA, BirthData::class.java)
                            ?: BirthData()
                    birthDataB =
                        bundle.getParcelable(KeyDefine.UserBirthDataB, BirthData::class.java)
                            ?: BirthData()
                } else {
                    LogUtil.e("BundleError", "UserBirthData key not found in bundle")
                    birthData = bundle.getParcelable(KeyDefine.UserBirthData) ?: BirthData()
                    birthDataA = bundle.getParcelable(KeyDefine.UserBirthDataA) ?: BirthData()
                    birthDataB = bundle.getParcelable(KeyDefine.UserBirthDataB) ?: BirthData()
                }

                isSystem = bundle.getBoolean(KeyDefine.IsSystem, false)
                isNavCelestial = false

                // 切換到主線程更新 UI
                withContext(Dispatchers.Main) {
                    chartSelect(chart)
                }
            }
        } catch (e: Exception) {
            LogUtil.e("初始化數據失敗" + e.message)
            withContext(Dispatchers.Main) {
                Toast.makeText(
                    this@SignDetailActivity,
                    "初始化數據失敗: ${e.message}",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    private fun initTab() {
        val adapter = RecyclerviewAdapter(this)
        binding.viewPager2.adapter = adapter
        binding.viewPager2.isUserInputEnabled = false

        TabLayoutMediators(
            binding.tabLayout, binding.viewPager2
        ) { tab, position ->
            when (position) {
                0 -> tab.text = getString(R.string.astrology_chart)
                1 -> tab.text = getString(R.string.astral_position)
                2 -> tab.text = getString(R.string.house_sign)
                3 -> tab.text = getString(R.string.planetary_aspects)
                4 -> tab.text = getString(R.string.score)
                5 -> tab.text = getString(R.string.proportion)
                6 -> tab.text = getString(R.string.firdaria_list)
                7 -> tab.text = getString(R.string.feature)
                8 -> tab.text = getString(R.string.interpretation)
                9 -> tab.text = getString(R.string.judgment)
            }
        }.attach()

        binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                if (tab != null) {
                    LogUtil.d(tab.text.toString())
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                if (tab != null) {
                    LogUtil.d(tab.text.toString())
                }
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                if (tab != null) {
                    LogUtil.d(tab.text.toString())
                }
            }
        })
        binding.viewPager2.offscreenPageLimit = 1 // 預加載
        initTabLayout()
    }

    class RecyclerviewAdapter(fragment: BaseActivity?) : FragmentStateAdapter(fragment!!) {
        private var fragments: ArrayList<Fragment> = arrayListOf(
            ChartFragment(),
            SignPositionFragment(),
            HouseFragment(),
            AspectFragment(),
            ScoreFragment(),
            ProportionFragment(),
            FirdariaFragment(),
            FeatureFragment(),
            InterpretationFragment(),
            JudgmentFragment(),
        )

        override fun getItemCount(): Int {
            return fragments.size
        }

        override fun createFragment(position: Int): Fragment {
            return fragments[position]
        }
    }

    private fun getEvent() {
        LiveEventBus.get(EventKey.UpdateChart, Char::class.java).observe(this) {
            chartSelect(chart)
        }
        LiveEventBus.get(EventKey.UpdateChartA, Long::class.java).observe(this) {
            when (chart) {
                Chart.Celestial -> {
                    birthData.birthday = it
                }

                Chart.SynastrySecondaryProgression,
                Chart.SynastryTertiaryProgression,
                Chart.CompositeSecondaryProgression,
                Chart.CompositeTertiaryProgression,
                Chart.DavisonSecondaryProgression,
                Chart.DavisonTertiaryProgression,
                Chart.SolarReturn,
                Chart.LunarReturn -> {
                    progressedTime.birthday = it
                }

                else -> {
                    birthDataA.birthday = it
                }
            }
            chartSelect(chart)
        }
        LiveEventBus.get(EventKey.UpdateChartB, Long::class.java).observe(this) {
            val timeString: String =
                FormatUtils.dateToString(Date(it), "yyyy/MM/dd HH:mm")
            LogUtil.d(timeString)
            when (chart) {
                Chart.TertiaryProgressionSynastry,
                Chart.SecondaryProgressionSynastry -> {
                    progressedTime.birthday = it
                }

                else -> {
                    birthDataB.birthday = it
                    progressedTime.birthday = it
                }
            }
            chartSelect(chart)
        }
        LiveEventBus.get(EventKey.UserBirthData, BirthData::class.java).observe(this) {
            birthDataA = it
            chartSelect(chart)
        }
        LiveEventBus.get(EventKey.AutoTransitCalculate, String::class.java).observe(this) {
            share(it)
        }
        // 監聽設定頁面返回事件，重新計算星盤數據
        LiveEventBus.get(EventKey.RefreshChart, Boolean::class.java).observe(this) {
            if (it) {
                LogUtil.d("Refreshing chart from settings")
                chartSelect(chart)
            }
        }
    }

    private fun share(json: String) {
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, json)
            type = "text/plain"
        }

        val shareIntent = Intent.createChooser(sendIntent, null)
        startActivity(shareIntent)
    }

    private fun onMenuItemClick(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_menu_share -> {
                share()
                return true
            }

            R.id.action_menu_detail -> {
                return true
            }

            R.id.action_menu_edit -> {
                edit()
                return true
            }

            R.id.action_menu_swap -> {
                val birthData: BirthData = birthDataA
                birthDataA = birthDataB
                birthDataB = birthData
                lifecycleScope.launch {
                    viewModel.doDouble(
                        appCompatActivity, birthDataA, birthDataB, progressedTime, chart
                    )
                    val name =
                        getString(R.string.both_name, birthDataA.name, birthDataB.name)
                    setTitle(chart, name)
                    setUserInfo(birthDataA, birthDataB, chart)
                }
                return true
            }

            R.id.action_menu_settings -> {
                SettingBottomSheetFragment().show(
                    supportFragmentManager, "SettingBottomSheetFragment"
                )
                return true
            }

            else -> return false
        }
    }

    private fun isVisibleTabLayout(index: Int, isVisible: Boolean) {
        binding.tabLayout.getTabAt(index)?.view!!.isVisible = isVisible
    }

    private fun chartSelect(chart: Chart) {
        LogUtil.setCurrentScreen(getString(chart.type), this.javaClass.simpleName)
        if (progressedTime.name.isEmpty()) {
            progressedTime = getCurrentTimeRecord(getString(chart.type))
        }
        progressedTime.name = getString(chart.type)
        when (chart) {
            Chart.Celestial -> {
                if (birthData.name.isEmpty()) {
                    birthData = getCurrentTimeRecord(getString(chart.type))
                }
                doSingle(birthData, chart)
                binding.navigationView.setCheckedItem(R.id.nav_celestial)
            }

            Chart.Natal -> {
                binding.toolbar.menu.findItem(R.id.action_menu_edit).isVisible = true
                binding.toolbar.menu.findItem(R.id.action_menu_share).isVisible = true
                isVisibleTabLayout(2, true)
                isVisibleTabLayout(4, false)
                if (BuildConfig.IS_DEV) {
                    isVisibleTabLayout(7, true)
                }
                binding.navigationView.setCheckedItem(R.id.nav_natal)
                doSingle(birthDataA, chart)
            }

            Chart.Transit -> {
                isVisibleTabLayout(2, false)
                binding.navigationView.setCheckedItem(R.id.nav_transits)
                doPrediction(birthDataA, progressedTime, chart)
            }

            Chart.SecondaryProgression -> {
                binding.navigationView.setCheckedItem(R.id.nav_secondary_progression)
                doPrediction(birthDataA, progressedTime, chart)
            }

            Chart.SecondaryProgressionSynastry -> {
                isVisibleTabLayout(2, false)
                binding.navigationView.setCheckedItem(R.id.nav_secondary_progression_synastry)
                doPrediction(birthDataA, progressedTime, chart)
            }

            Chart.TertiaryProgression -> {
                doPrediction(birthDataA, progressedTime, chart)
                binding.navigationView.setCheckedItem(R.id.nav_tertiary_progression)
            }

            Chart.TertiaryProgressionSynastry -> {
                doPrediction(birthDataA, progressedTime, chart)
                isVisibleTabLayout(2, false)
                binding.navigationView.setCheckedItem(R.id.nav_tertiary_progression_synastry)
            }

            Chart.SolarReturn -> {
                binding.navigationView.setCheckedItem(R.id.nav_solar_return)
                doPrediction(birthDataA, progressedTime, chart)
            }

            Chart.LunarReturn -> {
                binding.navigationView.setCheckedItem(R.id.nav_lunar_return)
                doPrediction(birthDataA, progressedTime, chart)
            }

            Chart.SolarArc -> {
                doPrediction(birthDataA, progressedTime, chart)
                binding.navigationView.setCheckedItem(R.id.nav_solar_arc)
                isVisibleTabLayout(2, false)
            }

            Chart.Synastry -> {
                binding.navigationView.setCheckedItem(R.id.nav_synastry)
                isVisibleTabLayout(2, false)
                isVisibleTabLayout(4, true)
                isVisibleTabLayout(5, false)
                binding.toolbar.menu.findItem(R.id.action_menu_swap).isVisible = true
                doDouble(birthDataA, birthDataB, chart)
            }

            Chart.SynastrySecondaryProgression -> {
                binding.navigationView.setCheckedItem(R.id.nav_synastry_secondary_progression)
                doDouble(birthDataA, birthDataB, chart)
            }

            Chart.SynastryTertiaryProgression -> {
                binding.navigationView.setCheckedItem(R.id.nav_synastry_tertiary_progression)
                doDouble(birthDataA, birthDataB, chart)
            }

            Chart.Composite -> {
                binding.navigationView.setCheckedItem(R.id.nav_composite)
                doDouble(birthDataA, birthDataB, chart)
            }

            Chart.CompositeSecondaryProgression -> {
                binding.navigationView.setCheckedItem(R.id.nav_composite_secondary_progression)
                doDouble(birthDataA, birthDataB, chart)
            }

            Chart.CompositeTertiaryProgression -> {
                binding.navigationView.setCheckedItem(R.id.nav_composite_tertiary_progression)
                doDouble(birthDataA, birthDataB, chart)
            }

            Chart.Davison -> {
                binding.navigationView.setCheckedItem(R.id.nav_davison)
                doDouble(birthDataA, birthDataB, chart)
            }

            Chart.DavisonSecondaryProgression -> {
                binding.navigationView.setCheckedItem(R.id.nav_davison_secondary_progression)
                doDouble(birthDataA, birthDataB, chart)
            }

            Chart.DavisonTertiaryProgression -> {
                binding.navigationView.setCheckedItem(R.id.nav_davison_tertiary_progression)
                doDouble(birthDataA, birthDataB, chart)
            }

            Chart.Marks -> {
                binding.toolbar.menu.findItem(R.id.action_menu_swap).isVisible = true
                binding.navigationView.setCheckedItem(R.id.nav_marks)
                doDouble(birthDataA, birthDataB, chart)
            }

            Chart.MarksSecondaryProgression -> {
                binding.toolbar.menu.findItem(R.id.action_menu_swap).isVisible = true
                binding.navigationView.setCheckedItem(R.id.nav_marks_secondary_progression)
                doDouble(birthDataA, birthDataB, chart)
            }

            Chart.MarksTertiaryProgression -> {
                binding.toolbar.menu.findItem(R.id.action_menu_swap).isVisible = true
                binding.navigationView.setCheckedItem(R.id.nav_marks_tertiary_progression)
                doDouble(birthDataA, birthDataB, chart)
            }

            Chart.Firdaria -> {
                binding.toolbar.menu.findItem(R.id.action_menu_edit).isVisible = true
                binding.toolbar.menu.findItem(R.id.action_menu_share).isVisible = true
                isVisibleTabLayout(2, true)
                isVisibleTabLayout(4, false)
                isVisibleTabLayout(6, true)
                binding.navigationView.setCheckedItem(R.id.nav_firdaria)

                launchWhenStarted {
                    setTitle(chart, birthDataA.name)
                    setUserInfo(birthDataA, progressedTime, chart)
                    viewModel.doDouble(
                        this@SignDetailActivity,
                        birthDataA,
                        progressedTime,
                        progressedTime,
                        chart
                    )
                }
            }
        }
    }

    private fun initTabLayout() {
        isVisibleTabLayout(2, true)
        isVisibleTabLayout(4, false)
        isVisibleTabLayout(6, false)
        isVisibleTabLayout(7, false)
        when (BuildConfig.FLAVOR) {
            "dev",
            "uat" -> {
                isVisibleTabLayout(8, true)
                isVisibleTabLayout(9, false)
            }

            else -> {
                isVisibleTabLayout(8, false)
                isVisibleTabLayout(9, false)
            }
        }

    }

    private fun itemSelect(menuItem: MenuItem) {
        binding.drawerLayout.close()
        menuItem.isChecked = false
        binding.toolbar.menu.findItem(R.id.action_menu_edit).isVisible = false
        binding.toolbar.menu.findItem(R.id.action_menu_share).isVisible = false
        binding.toolbar.menu.findItem(R.id.action_menu_swap).isVisible = false
        initTabLayout()

        when (menuItem.itemId) {
            R.id.nav_celestial -> {
                chart = Chart.Celestial
                isNavCelestial = true
                chartSelect(chart)
            }

            R.id.nav_natal -> {
                chart = Chart.Natal
                chartSelect(chart)
            }

            R.id.nav_transits -> {
                chart = Chart.Transit
                chartSelect(chart)
            }

            R.id.nav_secondary_progression -> {
                chart = Chart.SecondaryProgression
                chartSelect(chart)
            }

            R.id.nav_secondary_progression_synastry -> {
                chart = Chart.SecondaryProgressionSynastry
                chartSelect(chart)
            }

            R.id.nav_tertiary_progression -> {
                chart = Chart.TertiaryProgression
                chartSelect(chart)
            }

            R.id.nav_tertiary_progression_synastry -> {
                chart = Chart.TertiaryProgressionSynastry
                chartSelect(chart)
            }

            R.id.nav_solar_return -> {
                chart = Chart.SolarReturn
                chartSelect(chart)
            }

            R.id.nav_lunar_return -> {
                chart = Chart.LunarReturn
                chartSelect(chart)
            }

            R.id.nav_solar_arc -> {
                chart = Chart.SolarArc
                chartSelect(chart)
            }

            R.id.nav_synastry -> {
                showSelectionDialog(Chart.Synastry, menuItem)
            }

            R.id.nav_synastry_secondary_progression -> {
                showSelectionDialog(Chart.SynastrySecondaryProgression, menuItem)
            }

            R.id.nav_synastry_tertiary_progression -> {
                showSelectionDialog(Chart.SynastryTertiaryProgression, menuItem)
            }

            R.id.nav_composite -> {
                showSelectionDialog(Chart.Composite, menuItem)
            }

            R.id.nav_composite_secondary_progression -> {
                showSelectionDialog(Chart.CompositeSecondaryProgression, menuItem)
            }

            R.id.nav_composite_tertiary_progression -> {
                showSelectionDialog(Chart.CompositeTertiaryProgression, menuItem)
            }

            R.id.nav_davison -> {
                showSelectionDialog(Chart.Davison, menuItem)
            }

            R.id.nav_davison_secondary_progression -> {
                showSelectionDialog(Chart.DavisonSecondaryProgression, menuItem)
            }

            R.id.nav_davison_tertiary_progression -> {
                showSelectionDialog(Chart.DavisonTertiaryProgression, menuItem)
            }

            R.id.nav_marks -> {
                showSelectionDialog(Chart.Marks, menuItem)
            }

            R.id.nav_marks_secondary_progression -> {
                showSelectionDialog(Chart.MarksSecondaryProgression, menuItem)
            }

            R.id.nav_marks_tertiary_progression -> {
                showSelectionDialog(Chart.MarksTertiaryProgression, menuItem)
            }

            R.id.nav_firdaria -> {
                chart = Chart.Firdaria
                chartSelect(chart)
            }

            else -> {
                setTitle(chart, birthDataA.name)
                doSingle(birthDataA, Chart.Natal)
            }
        }
    }

    private fun showSelectionDialog(chartType: Chart, menuItem: MenuItem) {
        menuItem.isChecked = false
        val signRecordBox = ObjectBox.get().boxFor(BirthData::class.java)
        val signRecordList: List<BirthData> =
            signRecordBox?.query()?.orderDesc(BirthData_.id)?.build()?.find() ?: ArrayList()
        if (signRecordList.size < 2) {
            Toast.makeText(appCompatActivity, "尚無紀錄可供合盤", Toast.LENGTH_LONG).show()
            return
        }

        if (birthDataA.name.isNotEmpty() || birthDataB.name.isNotEmpty()) {
            doDouble(menuItem.title, chartType)
            return
        }
        val matchCallBack = object : SelectBottomSheetFragment.MatchCallBack {
            override fun onClick(list: List<BirthData?>) {
                if (list.size == 2) {
                    birthDataA = list[1]!!
                    birthDataB = list[0]!!
                    doDouble(menuItem.title, chartType)
                }
            }
        }
        SelectBottomSheetFragment.newInstance(matchCallBack, birthDataA, birthDataB).show(
            supportFragmentManager, "SelectBottomSheetFragment"
        )
    }

    private fun doDouble(title: CharSequence?, chartType: Chart) {
        binding.toolbar.title = title
        binding.toolbar.menu.findItem(R.id.action_menu_swap).isVisible = true
        when (chartType) {
            Chart.Synastry, Chart.SynastrySecondaryProgression, Chart.SynastryTertiaryProgression, Chart.Composite, Chart.CompositeSecondaryProgression, Chart.CompositeTertiaryProgression, Chart.Davison, Chart.DavisonSecondaryProgression, Chart.DavisonTertiaryProgression, Chart.Marks, Chart.MarksSecondaryProgression, Chart.MarksTertiaryProgression -> {
                chartSelect(chartType)
            }

            else -> {
                doDouble(birthDataA, birthDataB, chartType)
            }
        }
    }

    private fun setUserInfo(birthA: BirthData, birthB: BirthData?, chart: Chart) {
        binding.bottomSheet.tvName.text = birthA.name
        val timeString: String = FormatUtils.dateToString(Date(birthA.birthday), "yyyy/MM/dd HH:mm")
        binding.bottomSheet.tvBirthday.text = timeString
        binding.bottomSheet.tvLatitude.text =
            getString(R.string._3f).format(birthA.birthplaceLatitude)
        binding.bottomSheet.tvLongitude.text =
            getString(R.string._3f).format(birthA.birthplaceLongitude)
        binding.bottomSheet.ibEdit.setOnClickListener {
            val onClickListener = View.OnClickListener {
                birthDataA = it.tag as BirthData
                chartSelect(chart)
            }
            SingleSelectBottomSheetFragment.newInstance(onClickListener).show(
                supportFragmentManager, "SelectBottomSheetFragment"
            )
        }
        if (birthB != null) {
            binding.bottomSheet.lltRight.visibility = View.VISIBLE
            birthB.name.also { binding.bottomSheet.tvNameB.text = it }
            val timeStringB: String =
                FormatUtils.dateToString(Date(birthB.birthday), "yyyy/MM/dd HH:mm")
            binding.bottomSheet.tvBirthdayB.text = timeStringB
            binding.bottomSheet.tvLatitudeB.text =
                getString(R.string._3f).format(birthB.birthplaceLatitude)
            binding.bottomSheet.tvLongitudeB.text =
                getString(R.string._3f).format(birthB.birthplaceLongitude)
            binding.bottomSheet.lltRight.visibility = View.VISIBLE
            binding.bottomSheet.ibEditB.setOnClickListener {
                val onClickListener = View.OnClickListener {
                    birthDataB = it.tag as BirthData
                    chartSelect(chart)
                }
                SingleSelectBottomSheetFragment.newInstance(onClickListener).show(
                    supportFragmentManager, "SelectBottomSheetFragment"
                )
            }
        } else {
            binding.bottomSheet.lltRight.visibility = View.GONE
        }
    }

    private fun doSingle(birthData: BirthData, chart: Chart) {
        if (birthData.name.isEmpty()) {
            val onClickListener = View.OnClickListener {
                birthDataA = it.tag as BirthData
                doSingle(birthDataA, chart)
            }
            SingleSelectBottomSheetFragment.newInstance(onClickListener).show(
                supportFragmentManager,
                "SelectBottomSheetFragment"
            )
            return
        }
        setTitle(chart, birthData.name)
        setUserInfo(birthData, null, chart)
        lifecycleScope.launch {
            viewModel.doSingle(this@SignDetailActivity, birthData, chart)
        }
    }

    private fun doPrediction(dataA: BirthData, dataB: BirthData, chart: Chart) {
        if (dataA.name.isEmpty()) {
            val onClickListener = View.OnClickListener {
                val birthDataSelect = it.tag as BirthData
                birthDataA = birthDataSelect
                doDouble(birthDataSelect, dataB, chart)
            }
            SingleSelectBottomSheetFragment.newInstance(onClickListener).show(
                supportFragmentManager,
                "SelectBottomSheetFragment"
            )
            return
        }
        setUserInfo(dataA, dataB, chart)
        val name = getString(R.string.both_name, dataA.name, dataB.name)
        setTitle(chart, name)
        launchWhenStarted {
            viewModel.doDouble(
                this@SignDetailActivity, dataA, dataB, progressedTime, chart
            )
        }
    }

    private fun doDouble(dataA: BirthData, dataB: BirthData, chart: Chart) {
        if (dataA.name.isEmpty() || dataB.name.isEmpty()) {
            val matchCallBack = object : SelectBottomSheetFragment.MatchCallBack {
                override fun onClick(list: List<BirthData?>) {
                    if (list.size == 2) {
                        birthDataA = list[1]!!
                        birthDataB = list[0]!!
                        doDouble(birthDataA, birthDataB, chart)
                    }
                }
            }
            SelectBottomSheetFragment.newInstance(matchCallBack, birthDataA, birthDataB).show(
                supportFragmentManager, "SelectBottomSheetFragment"
            )
            return
        }
        setUserInfo(dataA, dataB, chart)
        val name = getString(R.string.both_name, dataA.name, dataB.name)
        setTitle(chart, name)
        launchWhenStarted {
            viewModel.doDouble(
                this@SignDetailActivity, dataA, dataB, progressedTime, chart
            )
        }
    }

    private fun getCurrentTimeRecord(name: String): BirthData {
        val calendar = Calendar.getInstance()
        val birthData = BirthData()
        birthData.name = name // "行運"
        birthData.birthday = calendar.timeInMillis
        birthData.birthplaceLatitude = birthDataA.birthplaceLatitude
        birthData.birthplaceLongitude = birthDataA.birthplaceLongitude

        return birthData
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        val bundle = getIntent().extras
        if (bundle != null) {
            initParams(bundle)
        }
    }

    private fun setTitle(chart: Chart, name: String) {
        Companion.chart = chart
        binding.toolbar.title = getString(chart.type)
        binding.toolbar.subtitle = name
    }

    override fun initParams(bundle: Bundle?) {
        mBundle = bundle
    }

    private fun share() {
        val gson = Gson()
        val dialog = MaterialDialog(appCompatActivity, MaterialDialog.DEFAULT_BEHAVIOR)
        dialog.message(null, "是否要隱藏個人出生資訊?", null)
        dialog.positiveButton(null, "是") {
            dialog.dismiss()
            birthDataA.isHide = true
            val data: String = gson.toJson(birthDataA)
            val bundle = Bundle()
            bundle.putString(QRCodeActivity.ARG_CONTENT, data)
            startActivity(QRCodeActivity::class.java, bundle)
        }
        dialog.negativeButton(null, "否") {
            dialog.dismiss()
            birthDataA.isHide = false
            val data: String = gson.toJson(birthDataA)
            val bundle = Bundle()
            bundle.putString(QRCodeActivity.ARG_CONTENT, data)
            startActivity(QRCodeActivity::class.java, bundle)
        }
        dialog.show()
    }

    private fun edit() {
        if (mBundle == null) {
            mBundle = Bundle()
        }
        if (!birthDataA.isHide) {
            BirthDataDialogFragment(birthDataA).show(supportFragmentManager, "")
        } else {
            Toast.makeText(this, "資訊隱藏無法編輯!", Toast.LENGTH_LONG).show()
        }
    }
}