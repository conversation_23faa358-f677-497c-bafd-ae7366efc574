package com.one.astrology.ui.fragment.dialog

import android.Manifest
import android.annotation.SuppressLint
import android.app.AlertDialog
import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.afollestad.date.year
import com.google.android.gms.maps.model.LatLng
import com.google.android.material.datepicker.MaterialDatePicker
import com.google.android.material.textfield.TextInputLayout
import com.google.android.material.timepicker.MaterialTimePicker
import com.google.android.material.timepicker.MaterialTimePicker.INPUT_MODE_KEYBOARD
import com.google.android.material.timepicker.TimeFormat
import com.jeremyliao.liveeventbus.LiveEventBus
import com.loper7.date_time_picker.DateTimeConfig
import com.loper7.date_time_picker.dialog.CardDatePickerDialog
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.constant.KeyDefine
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.model.City
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentBirthDataDialogBinding
import com.one.astrology.event.EventKey
import com.one.astrology.event.LatLngEvent
import com.one.astrology.ui.activity.MapActivity
import com.one.astrology.ui.activity.SignDetailActivity
import com.one.astrology.ui.adapter.CitySuggestionsAdapter
import com.one.astrology.ui.fragment.viewmodel.SharedViewModel
import com.one.astrology.util.LocationUtil
import com.one.astrology.util.LocationUtil.Companion.searchCitySuggestions
import com.one.astrology.util.astro.ChartUtils.toStorageValue
import com.one.core.util.FormatUtils
import com.one.core.util.InputTools
import com.one.core.util.LogUtil
import com.one.core.util.PermissionsUtil
import io.objectbox.Box
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.Date


/**
 * 出生資料頁
 */
class BirthDataDialogFragment : DialogFragment {
    // 回調接口，用於通知新增或更新的出生資料
    private var onDismissListener: ((BirthData?) -> Unit)? = null
    // R.layout.fragment_birth_data
    private var mDate: Date? = null
    private lateinit var materialDatePicker: MaterialDatePicker<Long>
    private lateinit var materialTimePicker: MaterialTimePicker
    private lateinit var birthData: BirthData
    private lateinit var itemBoxSignRecord: Box<BirthData?>
    private var latitude = -1.0
    private var longitude = -1.0
    private var placeType = 0
    private lateinit var binding: FragmentBirthDataDialogBinding
    private lateinit var dialog: AlertDialog
    private val calendar = Calendar.getInstance()
    private val sharedViewModel by viewModels<SharedViewModel>()
    private lateinit var citySuggestionsAdapter: CitySuggestionsAdapter

    constructor() {
        birthData = BirthData()
    }

    constructor(signRecord: BirthData) {
        birthData = signRecord
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val builder = AlertDialog.Builder(requireActivity())
        val inflater = requireActivity().layoutInflater
        binding = FragmentBirthDataDialogBinding.inflate(inflater, null, false)
        builder.setView(binding.root)
        initView()
        initDatePicker()
        initTimePickerDialog()
        initLocationInput()
        dialog = builder.create()
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
        return dialog
    }

    override fun onCancel(dialog: android.content.DialogInterface) {
        super.onCancel(dialog)
        // 對話框取消時調用回調函數，參數為null
        onDismissListener?.invoke(null)
    }

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("出生資料頁", this.javaClass.simpleName)
    }

    fun initView() {
        itemBoxSignRecord = ObjectBox.get().boxFor(BirthData::class.java)
        if (birthData.name.isNotEmpty()) {
            initData()
        } else {
            birthData = BirthData()
        }
        binding.cbDaylightSavingTime.isChecked = birthData.isDaylightSavingTime

        binding.tvDate.setOnClickListener {
            val calendar = Calendar.getInstance()
            calendar.year = calendar[Calendar.YEAR] - 20

            if (mDate != null) {
                calendar.time = mDate!!
            }
            val list = intArrayOf(
                DateTimeConfig.YEAR,
                DateTimeConfig.MONTH,
                DateTimeConfig.DAY,
                DateTimeConfig.HOUR,
                DateTimeConfig.MIN
            ).toMutableList()

            CardDatePickerDialog.builder(requireContext())
                .setTitle(getString(R.string.select_a_date_and_time_of_birth))
                .setBackGroundModel(CardDatePickerDialog.CARD)
                .setDefaultTime(calendar.timeInMillis)
                .setDisplayType(list)
                .setLabelText(
                    getString(R.string.year), getString(R.string.month),
                    getString(R.string.day), getString(R.string.time), getString(R.string.minute)
                )
                .showDateLabel(true)
                .showFocusDateInfo(true)
                .showBackNow(true)
                .setOnChoose(getString(R.string.choose)) { millisecond ->
                    val timeString: String =
                        FormatUtils.dateToString(
                            Date(millisecond),
                            getString(R.string.yyyy_mm_dd_hh_mm)
                        )
                    binding.tvDate.setText(timeString)
                    mDate = Date(millisecond)
                }
                .setOnCancel(getString(R.string.cancel)) { }
                .build().show()
        }
        binding.tvTime.setOnClickListener { timePickerDialog() }
        binding.btSave.setOnClickListener {
            addData(it)
        }
        binding.chipGroup.setOnCheckedStateChangeListener { group, _ ->
            // Responds to child chip checked/unchecked
            birthData.tag = when (group.checkedChipId) {
                R.id.chip_1 -> binding.chip1.text.toString()
                R.id.chip_2 -> binding.chip2.text.toString()
                R.id.chip_3 -> binding.chip3.text.toString()
                R.id.chip_4 -> binding.chip4.text.toString()
                R.id.chip_5 -> binding.chip5.text.toString()
                R.id.chip_6 -> binding.chip6.text.toString()
                R.id.chip_7 -> binding.chip7.text.toString()
                else -> binding.chip1.text.toString()
            }
        }

        latLngEvent()

        binding.btnGetCurrentLocation.setOnClickListener {
            getCurrentLocation()
        }

        binding.btnMap.setOnClickListener {
            placeType = 2
            val bundle = if (
                birthData.birthplaceLatitude != -1.0 && birthData.birthplaceLongitude != -1.0
            ) {
                MapActivity.createBundle(
                    birthData.birthplaceLatitude,
                    birthData.birthplaceLongitude
                )
            } else null
            startActivity(MapActivity::class.java, bundle)
        }

        binding.btnSearch.setOnClickListener {
            if (binding.etCityName.text.toString().isEmpty()) {
                return@setOnClickListener
            }
            lifecycleScope.launch {
                val latLng = LocationUtil.addressToLatLng(
                    requireContext(),
                    binding.etCityName.text.toString()
                )
                latitude = latLng.latitude
                longitude = latLng.longitude
                binding.tvLatitude.text = formatCoordinate(latLng.latitude)
                binding.tvLongitude.text = formatCoordinate(latLng.longitude)
            }
        }
    }

    private fun latLngEvent() {
        LiveEventBus.get(EventKey.LatLng, LatLngEvent::class.java)
            .observeStickyForever { latLngEvent: LatLngEvent? ->
                if (latLngEvent != null) {
                    latitude = latLngEvent.center.latitude
                    longitude = latLngEvent.center.longitude
                    binding.tvLatitude.text = formatCoordinate(latitude)
                    binding.tvLongitude.text = formatCoordinate(longitude)
                    // 更新經緯度顯示
                    updateCoordinateDisplay(latitude, longitude)

                    // 更新地址
                    updateLocationAddress(LatLng(latitude, longitude), binding.progressBarMap)

                    val text = "$latitude , $longitude"
                    LogUtil.d("經緯度 ：$text")
                }
            }
    }

    private fun addData(view: View) {
        if (TextUtils.isEmpty(binding.tvDate.text.toString()) || latitude == -1.0 || longitude == -1.0) {
            InputTools.HideKeyboard(view)
            Toast.makeText(
                requireActivity(),
                getString(R.string.please_fill_in_the_information),
                Toast.LENGTH_SHORT
            ).show()
            return
        }
        addRecord()
    }

    private fun initData() {
        binding.etName.setText(birthData.name)

        calendar.timeInMillis = birthData.birthday
        mDate = Date(calendar.timeInMillis)
        val timeString: String = FormatUtils.dateToString(mDate, "yyyy/MM/dd HH:mm")
        binding.tvDate.setText(timeString)

        binding.cbDaylightSavingTime.isChecked = birthData.isDaylightSavingTime
//        binding.tvTimezoneOffset.text =
//            "時區偏移：${if (birthData.timezoneOffset >= 0) "+" else ""}${"%.1f".format(birthData.timezoneOffset)} 小時"

        latitude = birthData.birthplaceLatitude
        longitude = birthData.birthplaceLongitude
        LogUtil.d("latLngBirth : $latitude , $longitude")

        if (latitude != -1.0 && longitude != -1.0) {
            binding.tvLatitude.text = formatCoordinate(latitude)
            binding.tvLongitude.text = formatCoordinate(longitude)
            if (!birthData.birthplaceArea.isNullOrEmpty()) {
                binding.etCityName.setText(birthData.birthplaceArea)
            } else {
                lifecycleScope.launch {
                    binding.progressBar.visibility = View.VISIBLE
                    val cityName =
                        LocationUtil.latLngToAddresses(LatLng(latitude, longitude), listOf("city"))
                    birthData.birthplaceArea = cityName
                    binding.etCityName.setText(cityName)
                    binding.progressBar.visibility = View.GONE
                }
            }
        }

        if (birthData.tag.isNotEmpty()) {
            when (birthData.tag) {
                binding.chip1.text.toString() -> binding.chip1.isChecked = true
                binding.chip2.text.toString() -> binding.chip2.isChecked = true
                binding.chip3.text.toString() -> binding.chip3.isChecked = true
                binding.chip4.text.toString() -> binding.chip4.isChecked = true
                binding.chip5.text.toString() -> binding.chip5.isChecked = true
                binding.chip6.text.toString() -> binding.chip6.isChecked = true
                binding.chip7.text.toString() -> binding.chip7.isChecked = true
                binding.chip8.text.toString() -> binding.chip8.isChecked = true
                else -> {}
            }
        }
    }

    private fun addRecord() {
        birthData.apply {
            name = binding.etName.text.toString()
            createTime = System.currentTimeMillis()
            isDaylightSavingTime = binding.cbDaylightSavingTime.isChecked

            val strTime = binding.tvDate.text.toString()
            val date = FormatUtils.stringToDate(strTime, "yyyy/MM/dd HH:mm")
            calendar.timeInMillis = date.time
            birthday = calendar.timeInMillis
            birthdayString = FormatUtils.longToString(birthday, "yyyy/MM/dd HH:mm")

            birthplaceLatitude = latitude
            birthplaceLongitude = longitude
        }

        mDate = calendar.time

        val hasValidCoordinates =
            birthData.birthplaceLatitude != -1.0 && birthData.birthplaceLongitude != -1.0
        val userInputCity = binding.etCityName.text.toString()

        when {
            hasValidCoordinates && userInputCity.isNotEmpty() -> {
                birthData.birthplaceArea = userInputCity
                saveAndNavigate()
            }

            hasValidCoordinates -> {
                lifecycleScope.launch {
                    birthData.birthplaceArea = LocationUtil.latLngToArea(
                        LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude)
                    )
                    saveAndNavigate()
                }
            }

            else -> {
                saveAndNavigate()
            }
        }
    }

    private fun saveAndNavigate() {
        itemBoxSignRecord.put(birthData)
        sharedViewModel.data.postValue("")
        toSignDetailActivity()
        LiveEventBus.get<BirthData>(EventKey.UserBirthData).postDelay(birthData, 0)

        // 調用回調函數
        onDismissListener?.invoke(birthData)

        dialog.dismiss()
    }


    private fun toSignDetailActivity() {
        val bundle = Bundle()
        bundle.putString(KeyDefine.Chart, Chart.Natal.toStorageValue())
        bundle.putParcelable(KeyDefine.UserBirthDataA, birthData)
        startActivity(SignDetailActivity::class.java, bundle)
    }

    private fun startActivity(clz: Class<*>?, bundle: Bundle?) {
        val intent = Intent()
        intent.setClass(requireContext(), clz!!)
        if (bundle != null) {
            intent.putExtras(bundle)
        }
        startActivity(intent)
    }

    private fun initDatePicker() {
        val calendar = Calendar.getInstance()
//        calendar.year = calendar[Calendar.YEAR] - 25

        if (mDate != null) {
            calendar.time = mDate!!
        }

        materialDatePicker =
            MaterialDatePicker.Builder.datePicker()
                .setTitleText(getString(R.string.select_a_date_of_birth))
                .setSelection(calendar.timeInMillis)
//                .setInputMode(MaterialDatePicker.INPUT_MODE_TEXT)
                .build()

        materialDatePicker.addOnPositiveButtonClickListener {
            val timeString: String = FormatUtils.dateToString(
                Date(it),
                getString(R.string.yyyy_mm_dd)
            )
            binding.tvDate.setText(timeString)
        }
    }

    private fun initTimePickerDialog() {
        val calendar = Calendar.getInstance()
        if (mDate != null) {
            calendar.time = mDate!!
        }
        materialTimePicker =
            MaterialTimePicker.Builder()
                .setTimeFormat(TimeFormat.CLOCK_12H)
                .setHour(calendar.get(Calendar.HOUR_OF_DAY))
                .setMinute(calendar.get(Calendar.MINUTE))
                .setTitleText(getString(R.string.select_the_time_of_birth))
                .build()
        MaterialTimePicker.Builder().setInputMode(INPUT_MODE_KEYBOARD)

        materialTimePicker.addOnPositiveButtonClickListener {
            val newHour: Int = materialTimePicker.hour
            val newMinute: Int = materialTimePicker.minute
            val hour = String.format("%02d", newHour)
            val minute = String.format("%02d", newMinute)
            binding.tvTime.setText(getString(R.string.time_h_m, hour, minute))
        }
    }

    private fun timePickerDialog() {
        if (!materialTimePicker.isVisible) {
            materialTimePicker.show(requireActivity().supportFragmentManager, "")
        }
    }

    private fun initLocationInput() {
        // 初始化城市建議列表適配器
        citySuggestionsAdapter = CitySuggestionsAdapter { city ->
            updateBirthplace(city)
            binding.rvCitySuggestions.visibility = View.GONE
        }

        binding.rvCitySuggestions.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = citySuggestionsAdapter
        }

        // 設置出生地輸入
        setupBirthplaceInput()

        // 設置GPS按鈕
        binding.etCityName.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                val query = binding.etCityName.text.toString()
                if (query.isNotEmpty()) {
                    lifecycleScope.launch {
                        try {
                            val suggestions = searchCitySuggestions(query)
                            citySuggestionsAdapter.submitList(suggestions)
                            binding.rvCitySuggestions.visibility =
                                if (suggestions.isNotEmpty()) View.VISIBLE else View.GONE
                        } catch (e: Exception) {
                            e.message?.let { LogUtil.e("搜尋城市建議失敗", it) }
                            Toast.makeText(context, "搜尋城市建議失敗，請重試", Toast.LENGTH_SHORT)
                                .show()
                        }
                    }
                }
                true
            } else {
                false
            }
        }

        // 設置GPS按鈕點擊事件
        (binding.etCityName.parent as? TextInputLayout)?.setEndIconOnClickListener {
            getCurrentLocation()
        }
    }

    private fun setupBirthplaceInput() {
        // 設置地點搜尋
        binding.etCityName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                s?.toString()?.let { query ->
                    if (query.length >= 2) {
                        lifecycleScope.launch {
                            try {
                                val suggestions = searchCitySuggestions(query)
                                citySuggestionsAdapter.submitList(suggestions)
                                binding.rvCitySuggestions.visibility =
                                    if (suggestions.isNotEmpty()) View.VISIBLE else View.GONE
                            } catch (e: Exception) {
                                e.message?.let { LogUtil.e("搜尋城市建議失敗", it) }
                                context?.let { safeContext ->
                                    Toast.makeText(
                                        safeContext,
                                        "搜尋城市建議失敗，請重試",
                                        Toast.LENGTH_SHORT
                                    ).show()
                                }
                            }
                        }
                    } else {
                        binding.rvCitySuggestions.visibility = View.GONE
                    }
                }
            }
        })
    }

    private fun updateBirthplace(city: City) {
        latitude = city.latitude
        longitude = city.longitude
        birthData.birthplaceLatitude = latitude
        birthData.birthplaceLongitude = longitude

        // 更新經緯度顯示
        updateCoordinateDisplay(latitude, longitude)

        // 更新地址顯示
        updateLocationAddress(LatLng(latitude, longitude), binding.progressBarGetCurrentLocation)
    }

    private fun updateLocationAddress(latLng: LatLng, view: View) {
        lifecycleScope.launch {
            view.visibility = View.VISIBLE
            try {
                val cityName = LocationUtil.latLngToAddresses(latLng, listOf("city"))
                birthData.birthplaceArea = cityName
                binding.etCityName.setText(cityName)
            } catch (e: Exception) {
                LogUtil.e("獲取地址失敗", e.message ?: "Unknown error")
                if (isAdded) {
                    Toast.makeText(
                        requireContext(),
                        "獲取地址失敗，請重試",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            } finally {
                view.visibility = View.GONE
            }
        }
    }

    private fun updateCoordinateDisplay(lat: Double, lng: Double) {
        binding.tvLatitude.text = formatCoordinate(lat)
        binding.tvLongitude.text = formatCoordinate(lng)
    }

    private fun formatAddress(address: String): String {
        return address.split(",")[1].trim()
    }

    @SuppressLint("DefaultLocale")
    private fun formatCoordinate(value: Double): String {
        return String.format("%.3f", value)
    }

    private fun getCurrentLocation() {
        locationPermissionLauncher.launch(Manifest.permission.ACCESS_FINE_LOCATION)
    }

    private val locationPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            LocationUtil.getCurrentLocation(
                requireActivity(),
                PermissionsUtil.LOCATION_PERMISSION_REQUEST_CODE
            ) { location ->
                latitude = location.latitude
                longitude = location.longitude

                birthData.birthplaceLatitude = latitude
                birthData.birthplaceLongitude = longitude

                // 更新經緯度顯示
                updateCoordinateDisplay(latitude, longitude)

                // 更新地址
                updateLocationAddress(
                    LatLng(latitude, longitude),
                    binding.progressBarGetCurrentLocation
                )
            }
            if (!isGranted) {
                LogUtil.d("定位權限被拒絕")
                Toast.makeText(
                    requireContext(),
                    "需要定位權限才能使用此功能喔！",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }

    /**
     * 設置對話框關閉後的回調
     * @param listener 回調函數，參數為新增或更新的出生資料，如果為空則表示取消
     */
    fun setOnDismissListener(listener: (BirthData?) -> Unit) {
        this.onDismissListener = listener
    }
}