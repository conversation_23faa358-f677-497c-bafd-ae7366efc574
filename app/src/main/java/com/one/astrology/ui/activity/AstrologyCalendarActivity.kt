package com.one.astrology.ui.activity

import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowBackIos
import androidx.compose.material.icons.filled.ArrowForwardIos
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.one.astrology.R
import com.one.astrology.constant.KeyDefine
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.model.AstrologyEvent
import com.one.astrology.data.model.DailyAstrologyInfo
import com.one.astrology.data.type.Chart
import com.one.astrology.util.EncryptedSPUtil
import com.one.astrology.util.astro.ChartUtils.toStorageValue
import com.one.astrology.viewmodel.AstrologyCalendarViewModel
import dagger.hilt.android.AndroidEntryPoint
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * 星象日曆 Activity
 */
@AndroidEntryPoint
class AstrologyCalendarActivity : AppCompatActivity() {

    private val viewModel by viewModels<AstrologyCalendarViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            MaterialTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    AstrologyCalendarScreen(
                        viewModel = viewModel,
                        onBackPressed = { finish() }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AstrologyCalendarScreen(
    viewModel: AstrologyCalendarViewModel,
    onBackPressed: () -> Unit
) {
    val currentMonth by viewModel.currentMonth.collectAsState()
    val selectedDate by viewModel.selectedDate.collectAsState()
    val dailyAstrologyInfo by viewModel.dailyAstrologyInfo.collectAsState()
    val dailyEvents by viewModel.dailyEvents.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()

    val calendar = Calendar.getInstance()
    calendar.time = currentMonth

    val context = LocalContext.current

    // 當選擇的日期改變時，載入星象數據
    LaunchedEffect(selectedDate) {
        selectedDate?.let {
            viewModel.loadAstrologyDataForSelectedDate(context as AppCompatActivity)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        // 頂部工具欄
        TopAppBar(
            title = {
                Text(
                    text = "星象日曆",
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackPressed) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color.White
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = colorResource(id = R.color.colorPrimary)
            )
        )

        // 月份導航
        MonthNavigationBar(
            currentMonth = currentMonth,
            onPreviousMonth = { viewModel.previousMonth() },
            onNextMonth = { viewModel.nextMonth() }
        )

        // 星期標題
        WeekdayHeader()

        // 日曆網格
        CalendarGrid(
            currentMonth = currentMonth,
            selectedDate = selectedDate,
            onDateSelected = { date ->
                viewModel.selectDate(date)
            }
        )

        // 選中日期的星象信息
        if (selectedDate != null) {
            Spacer(modifier = Modifier.height(16.dp))

            if (isLoading) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = colorResource(id = R.color.colorPrimary)
                    )
                }
            } else {
                // 星象事件卡片
                if (dailyEvents.isNotEmpty()) {
                    AstrologyEventsCard(
                        events = dailyEvents,
                        selectedDate = selectedDate!!,
                        onEventClick = { event, date ->
                            // 點擊事件進入星盤頁面
                            navigateToChart(context as AstrologyCalendarActivity, event, date)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 8.dp)
                    )

                    Spacer(modifier = Modifier.height(12.dp))
                }

                // 每日星象信息卡片
                if (dailyAstrologyInfo != null) {
                    DailyAstrologyInfoCard(
                        dailyInfo = dailyAstrologyInfo!!,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun MonthNavigationBar(
    currentMonth: Date,
    onPreviousMonth: () -> Unit,
    onNextMonth: () -> Unit
) {
    val monthFormat = SimpleDateFormat("yyyy年 MM月", Locale.getDefault())

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onPreviousMonth) {
            Icon(
                imageVector = Icons.Default.ArrowBackIos,
                contentDescription = "上個月",
                tint = colorResource(id = R.color.colorPrimary)
            )
        }

        Text(
            text = monthFormat.format(currentMonth),
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = colorResource(id = R.color.colorPrimary)
        )

        IconButton(onClick = onNextMonth) {
            Icon(
                imageVector = Icons.Default.ArrowForwardIos,
                contentDescription = "下個月",
                tint = colorResource(id = R.color.colorPrimary)
            )
        }
    }
}

@Composable
private fun WeekdayHeader() {
    val weekdays = listOf("日", "一", "二", "三", "四", "五", "六")

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        weekdays.forEach { weekday ->
            Box(
                modifier = Modifier
                    .weight(1f)
                    .padding(4.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = weekday,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Gray,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
private fun CalendarGrid(
    currentMonth: Date,
    selectedDate: Date?,
    onDateSelected: (Date) -> Unit
) {
    val calendar = Calendar.getInstance()
    calendar.time = currentMonth
    calendar.set(Calendar.DAY_OF_MONTH, 1)

    // 獲取月份第一天是星期幾
    val firstDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1

    // 獲取這個月有多少天
    val daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH)

    // 創建日期列表
    val dates = mutableListOf<Date?>()

    // 添加空白日期（上個月的日期）
    repeat(firstDayOfWeek) {
        dates.add(null)
    }

    // 添加本月的日期
    for (day in 1..daysInMonth) {
        calendar.set(Calendar.DAY_OF_MONTH, day)
        dates.add(Date(calendar.timeInMillis))
    }

    LazyVerticalGrid(
        columns = GridCells.Fixed(7),
        modifier = Modifier.padding(horizontal = 16.dp),
        contentPadding = PaddingValues(4.dp)
    ) {
        items(dates) { date ->
            CalendarDayItem(
                date = date,
                isSelected = date != null && selectedDate != null &&
                    isSameDay(date, selectedDate),
                isToday = date != null && isSameDay(date, Date()),
                onDateSelected = { date?.let(onDateSelected) }
            )
        }
    }
}

@Composable
private fun CalendarDayItem(
    date: Date?,
    isSelected: Boolean,
    isToday: Boolean,
    onDateSelected: () -> Unit
) {
    val dayFormat = SimpleDateFormat("d", Locale.getDefault())

    Box(
        modifier = Modifier
            .aspectRatio(1f)
            .padding(2.dp)
            .clip(CircleShape)
            .background(
                when {
                    isSelected -> colorResource(id = R.color.colorPrimary)
                    isToday -> colorResource(id = R.color.colorPrimary).copy(alpha = 0.2f)
                    else -> Color.Transparent
                }
            )
            .border(
                width = if (isToday && !isSelected) 1.dp else 0.dp,
                color = colorResource(id = R.color.colorPrimary),
                shape = CircleShape
            )
            .clickable(enabled = date != null) { onDateSelected() },
        contentAlignment = Alignment.Center
    ) {
        if (date != null) {
            Text(
                text = dayFormat.format(date),
                fontSize = 14.sp,
                fontWeight = if (isSelected || isToday) FontWeight.Bold else FontWeight.Normal,
                color = when {
                    isSelected -> Color.White
                    isToday -> colorResource(id = R.color.colorPrimary)
                    else -> Color.Black
                },
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun DailyAstrologyInfoCard(
    dailyInfo: DailyAstrologyInfo,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "${dailyInfo.date} 星象",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = colorResource(id = R.color.colorPrimary)
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 顯示主要行星位置
            Text(
                text = "主要行星位置",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Gray
            )

            Spacer(modifier = Modifier.height(8.dp))

            dailyInfo.planets.take(5).forEach { planet ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 2.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = planet.chName,
                        fontSize = 14.sp,
                        color = Color.Black
                    )

                    Text(
                        text = "${planet.signBean.chName} ${String.format("%.1f", planet.longitude % 30)}°",
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }
            }

            if (dailyInfo.importantAspects.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))

                Text(
                    text = "重要相位",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Gray
                )

                Spacer(modifier = Modifier.height(8.dp))

                dailyInfo.importantAspects.take(3).forEach { aspect ->
                    Text(
                        text = aspect,
                        fontSize = 12.sp,
                        color = Color.Black,
                        modifier = Modifier.padding(vertical = 1.dp)
                    )
                }
            }
        }
    }
}

/**
 * 星象事件卡片
 */
@Composable
private fun AstrologyEventsCard(
    events: List<AstrologyEvent>,
    selectedDate: Date,
    onEventClick: (AstrologyEvent, Date) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "星象事件",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = colorResource(id = R.color.colorPrimary)
            )

            Spacer(modifier = Modifier.height(12.dp))

            events.take(6).forEach { event ->
                AstrologyEventItem(
                    event = event,
                    onClick = { onEventClick(event, selectedDate) }
                )
                Spacer(modifier = Modifier.height(8.dp))
            }

            if (events.size > 6) {
                Text(
                    text = "還有 ${events.size - 6} 個事件...",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }
    }
}

/**
 * 單個星象事件項目
 */
@Composable
private fun AstrologyEventItem(
    event: AstrologyEvent,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
        // 事件圖標
        Box(
            modifier = Modifier
                .size(24.dp)
                .background(
                    color = event.getEventColor().copy(alpha = 0.2f),
                    shape = CircleShape
                ),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = event.getEventIcon(),
                fontSize = 12.sp,
                color = event.getEventColor()
            )
        }

        Spacer(modifier = Modifier.width(12.dp))

        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = event.title,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Black
            )

            if (event.description.isNotEmpty()) {
                Text(
                    text = event.description,
                    fontSize = 12.sp,
                    color = Color.Gray,
                    maxLines = 1
                )
            }
        }

        // 重要性指示器
        when (event.importance) {
            com.one.astrology.data.model.EventImportance.CRITICAL -> {
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(
                            color = Color.Red,
                            shape = CircleShape
                        )
                )
            }
            com.one.astrology.data.model.EventImportance.HIGH -> {
                Box(
                    modifier = Modifier
                        .size(6.dp)
                        .background(
                            color = Color(0xFFFF9800),
                            shape = CircleShape
                        )
                )
            }
            else -> {
                // 不顯示指示器
            }
        }
        }
    }
}

/**
 * 導航到星盤頁面
 */
private fun navigateToChart(
    activity: AstrologyCalendarActivity,
    event: AstrologyEvent,
    selectedDate: Date
) {
    try {
        // 創建天象盤的出生資料
        val celestialBirthData = createCelestialBirthData(activity, selectedDate)

        // 根據事件類型選擇對應的星盤類型
        val chartType = when (event.type) {
            com.one.astrology.data.model.EventType.PLANET_ASPECT,
            com.one.astrology.data.model.EventType.PLANET_SIGN_CHANGE,
            com.one.astrology.data.model.EventType.PLANET_RETROGRADE -> Chart.Celestial
            com.one.astrology.data.model.EventType.MOON_PHASE -> Chart.Celestial
            com.one.astrology.data.model.EventType.SOLAR_TERM -> Chart.Celestial
            com.one.astrology.data.model.EventType.ECLIPSE -> Chart.Celestial
        }

        // 創建 Bundle 並導航
        val bundle = Bundle().apply {
            putParcelable(KeyDefine.UserBirthDataA, celestialBirthData)
            putString(KeyDefine.Chart, chartType.toStorageValue())
        }

        val intent = Intent(activity, SignDetailActivity::class.java)
        intent.putExtras(bundle)
        activity.startActivity(intent)

    } catch (e: Exception) {
        com.one.core.util.LogUtil.e("導航到星盤頁面時發生錯誤: ${e.message}")
    }
}

/**
 * 創建天象盤的出生資料
 */
private fun createCelestialBirthData(
    activity: AstrologyCalendarActivity,
    selectedDate: Date
): BirthData {
    // 獲取預設地點
    val defaultLocation = EncryptedSPUtil.getSavedDailyAstrologyLocation(activity)?.first
        ?: com.google.android.gms.maps.model.LatLng(25.0330, 121.5654) // 預設台北

    // 設定時間為當天中午12點
    val calendar = Calendar.getInstance()
    calendar.time = selectedDate
//    calendar.set(Calendar.HOUR_OF_DAY, 12)
//    calendar.set(Calendar.MINUTE, 0)
//    calendar.set(Calendar.SECOND, 0)
//    calendar.set(Calendar.MILLISECOND, 0)

    return BirthData().apply {
        name = "天象盤 - ${SimpleDateFormat("yyyy/MM/dd", Locale.getDefault()).format(selectedDate)}"
        birthday = calendar.timeInMillis
        birthplaceLatitude = defaultLocation.latitude
        birthplaceLongitude = defaultLocation.longitude
        birthplaceArea = "天象觀測地點"
        isDaylightSavingTime = false
    }
}

private fun isSameDay(date1: Date, date2: Date): Boolean {
    val cal1 = Calendar.getInstance().apply { time = date1 }
    val cal2 = Calendar.getInstance().apply { time = date2 }

    return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
            cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
}
