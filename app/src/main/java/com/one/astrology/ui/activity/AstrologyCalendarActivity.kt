package com.one.astrology.ui.activity

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowBackIos
import androidx.compose.material.icons.filled.ArrowForwardIos
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.one.astrology.R
import com.one.astrology.data.model.DailyAstrologyInfo
import com.one.astrology.viewmodel.AstrologyCalendarViewModel
import dagger.hilt.android.AndroidEntryPoint
import java.text.SimpleDateFormat
import java.util.*

/**
 * 星象日曆 Activity
 */
@AndroidEntryPoint
class AstrologyCalendarActivity : AppCompatActivity() {

    private val viewModel by viewModels<AstrologyCalendarViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            MaterialTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    AstrologyCalendarScreen(
                        viewModel = viewModel,
                        onBackPressed = { finish() }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AstrologyCalendarScreen(
    viewModel: AstrologyCalendarViewModel,
    onBackPressed: () -> Unit
) {
    val currentMonth by viewModel.currentMonth.collectAsState()
    val selectedDate by viewModel.selectedDate.collectAsState()
    val dailyAstrologyInfo by viewModel.dailyAstrologyInfo.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    
    val calendar = Calendar.getInstance()
    calendar.time = currentMonth
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        // 頂部工具欄
        TopAppBar(
            title = {
                Text(
                    text = "星象日曆",
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackPressed) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color.White
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = colorResource(id = R.color.colorPrimary)
            )
        )
        
        // 月份導航
        MonthNavigationBar(
            currentMonth = currentMonth,
            onPreviousMonth = { viewModel.previousMonth() },
            onNextMonth = { viewModel.nextMonth() }
        )
        
        // 星期標題
        WeekdayHeader()
        
        // 日曆網格
        CalendarGrid(
            currentMonth = currentMonth,
            selectedDate = selectedDate,
            onDateSelected = { date ->
                viewModel.selectDate(date)
            }
        )
        
        // 選中日期的星象信息
        if (selectedDate != null) {
            Spacer(modifier = Modifier.height(16.dp))
            
            if (isLoading) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = colorResource(id = R.color.colorPrimary)
                    )
                }
            } else if (dailyAstrologyInfo != null) {
                DailyAstrologyInfoCard(
                    dailyInfo = dailyAstrologyInfo!!,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                )
            }
        }
    }
}

@Composable
private fun MonthNavigationBar(
    currentMonth: Date,
    onPreviousMonth: () -> Unit,
    onNextMonth: () -> Unit
) {
    val monthFormat = SimpleDateFormat("yyyy年 MM月", Locale.getDefault())
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onPreviousMonth) {
            Icon(
                imageVector = Icons.Default.ArrowBackIos,
                contentDescription = "上個月",
                tint = colorResource(id = R.color.colorPrimary)
            )
        }
        
        Text(
            text = monthFormat.format(currentMonth),
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = colorResource(id = R.color.colorPrimary)
        )
        
        IconButton(onClick = onNextMonth) {
            Icon(
                imageVector = Icons.Default.ArrowForwardIos,
                contentDescription = "下個月",
                tint = colorResource(id = R.color.colorPrimary)
            )
        }
    }
}

@Composable
private fun WeekdayHeader() {
    val weekdays = listOf("日", "一", "二", "三", "四", "五", "六")
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        weekdays.forEach { weekday ->
            Box(
                modifier = Modifier
                    .weight(1f)
                    .padding(4.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = weekday,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Gray,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
private fun CalendarGrid(
    currentMonth: Date,
    selectedDate: Date?,
    onDateSelected: (Date) -> Unit
) {
    val calendar = Calendar.getInstance()
    calendar.time = currentMonth
    calendar.set(Calendar.DAY_OF_MONTH, 1)
    
    // 獲取月份第一天是星期幾
    val firstDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1
    
    // 獲取這個月有多少天
    val daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
    
    // 創建日期列表
    val dates = mutableListOf<Date?>()
    
    // 添加空白日期（上個月的日期）
    repeat(firstDayOfWeek) {
        dates.add(null)
    }
    
    // 添加本月的日期
    for (day in 1..daysInMonth) {
        calendar.set(Calendar.DAY_OF_MONTH, day)
        dates.add(Date(calendar.timeInMillis))
    }
    
    LazyVerticalGrid(
        columns = GridCells.Fixed(7),
        modifier = Modifier.padding(horizontal = 16.dp),
        contentPadding = PaddingValues(4.dp)
    ) {
        items(dates) { date ->
            CalendarDayItem(
                date = date,
                isSelected = date != null && selectedDate != null && 
                    isSameDay(date, selectedDate),
                isToday = date != null && isSameDay(date, Date()),
                onDateSelected = { date?.let(onDateSelected) }
            )
        }
    }
}

@Composable
private fun CalendarDayItem(
    date: Date?,
    isSelected: Boolean,
    isToday: Boolean,
    onDateSelected: () -> Unit
) {
    val dayFormat = SimpleDateFormat("d", Locale.getDefault())
    
    Box(
        modifier = Modifier
            .aspectRatio(1f)
            .padding(2.dp)
            .clip(CircleShape)
            .background(
                when {
                    isSelected -> colorResource(id = R.color.colorPrimary)
                    isToday -> colorResource(id = R.color.colorPrimary).copy(alpha = 0.2f)
                    else -> Color.Transparent
                }
            )
            .border(
                width = if (isToday && !isSelected) 1.dp else 0.dp,
                color = colorResource(id = R.color.colorPrimary),
                shape = CircleShape
            )
            .clickable(enabled = date != null) { onDateSelected() },
        contentAlignment = Alignment.Center
    ) {
        if (date != null) {
            Text(
                text = dayFormat.format(date),
                fontSize = 14.sp,
                fontWeight = if (isSelected || isToday) FontWeight.Bold else FontWeight.Normal,
                color = when {
                    isSelected -> Color.White
                    isToday -> colorResource(id = R.color.colorPrimary)
                    else -> Color.Black
                },
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun DailyAstrologyInfoCard(
    dailyInfo: DailyAstrologyInfo,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "${dailyInfo.date} 星象",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = colorResource(id = R.color.colorPrimary)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 顯示主要行星位置
            Text(
                text = "主要行星位置",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Gray
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            dailyInfo.planets.take(5).forEach { planet ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 2.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = planet.chName,
                        fontSize = 14.sp,
                        color = Color.Black
                    )
                    
                    Text(
                        text = "${planet.findPlantSign(planet.longitude).chName} ${String.format("%.1f", planet.longitude % 30)}°",
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }
            }
            
            if (dailyInfo.importantAspects.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                
                Text(
                    text = "重要相位",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Gray
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                dailyInfo.importantAspects.take(3).forEach { aspect ->
                    Text(
                        text = aspect,
                        fontSize = 12.sp,
                        color = Color.Black,
                        modifier = Modifier.padding(vertical = 1.dp)
                    )
                }
            }
        }
    }
}

private fun isSameDay(date1: Date, date2: Date): Boolean {
    val cal1 = Calendar.getInstance().apply { time = date1 }
    val cal2 = Calendar.getInstance().apply { time = date2 }
    
    return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
            cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
}
