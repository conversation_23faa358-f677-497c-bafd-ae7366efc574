package com.one.astrology.ui.fragment.viewmodel.calculate

import android.content.Context
import androidx.lifecycle.ViewModel
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.data.Horoscope
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.SignBean
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.event.MatchEvent
import com.one.astrology.util.AssetsToObjectUtil
import com.one.astrology.util.EphemerisUtil
import com.one.core.util.LogUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.Calendar
import javax.inject.Inject
import kotlin.math.floor

@HiltViewModel
open class BaseCalculateViewModel @Inject constructor(
) : ViewModel() {

    val _loading = MutableStateFlow(false)
    val loading = _loading.asSharedFlow()

    var matchEvent = MatchEvent()

    private val _matchEventFlow = MutableStateFlow<MatchEvent>(MatchEvent())
    val matchEventFlow: StateFlow<MatchEvent> = _matchEventFlow.asStateFlow()

    fun updateMatchEvent(matchEvent: MatchEvent) {
        _matchEventFlow.value = matchEvent
    }

    fun getFirdaria(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData
    ): MatchEvent {
        val horoscopeA = EphemerisUtil.calculate(
            context,
            Chart.Firdaria,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude)
        )
        val horoscopeB = EphemerisUtil.calculate(
            context,
            Chart.Firdaria,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude)
        )
        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        return matchEvent
    }

    fun getBasic(context: Context, chart: Chart, birthData: BirthData): MatchEvent {
        val horoscope = EphemerisUtil.calculate(
            context,
            chart,
            birthData.name,
            birthData.birthday,
            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude)
        )
        matchEvent.horoscopeA = horoscope
        matchEvent.horoscopeB = horoscope
        return matchEvent
    }

    fun getSolarReturn(context: Context, birthData: BirthData, progressedTime: BirthData): MatchEvent {
        matchEvent.birthDataA = birthData
        matchEvent.chartType = Chart.SolarReturn
        var residenceLatitude = birthData.residenceLatitude
        if (residenceLatitude == null) {
            residenceLatitude = birthData.birthplaceLatitude
        }
        var residenceLongitude = birthData.residenceLongitude
        if (residenceLongitude == null) {
            residenceLongitude = birthData.birthplaceLongitude
        }
        val horoscopeB = EphemerisUtil.solarReturn(
            context,
            birthData.name,
            birthData.birthday,
            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude),
            progressedTime,
            LatLng(residenceLatitude, residenceLongitude),
            birthData.isDaylightSavingTime
        )
        matchEvent.timeStamp = horoscopeB.birthdayTime

//        val horoscopeA = Horoscope(birthData)
        horoscopeB.birthdayTime = progressedTime.birthday

        matchEvent.horoscopeA = horoscopeB
        matchEvent.horoscopeB = horoscopeB
        return matchEvent
    }

    fun getLunarReturn(context: Context, birthData: BirthData, progressedTime: BirthData): MatchEvent {
//        val matchEvent = MatchEvent()
        matchEvent.birthDataA = birthData
        matchEvent.chartType = Chart.LunarReturn
        var residenceLatitude = birthData.residenceLatitude
        if (residenceLatitude == null) {
            residenceLatitude = birthData.birthplaceLatitude
        }
        var residenceLongitude = birthData.residenceLongitude
        if (residenceLongitude == null) {
            residenceLongitude = birthData.birthplaceLongitude
        }
        val horoscopeB = EphemerisUtil.lunarReturn(
            context,
            birthData.name,
            birthData.birthday,
            LatLng(residenceLatitude, residenceLongitude),
            progressedTime,
            birthData.isDaylightSavingTime
        )
        matchEvent.timeStamp = horoscopeB.birthdayTime

//        val horoscopeA = Horoscope(birthData)
        horoscopeB.birthdayTime = progressedTime.birthday

        matchEvent.horoscopeA = horoscopeB
        matchEvent.horoscopeB = horoscopeB
        return matchEvent
    }

//    fun getSolarArc(context: Context, birthData: BirthData, calendar: Calendar): MatchEvent {
//        val angle = EphemerisUtil.getPlanetAngle(
//            context,
//            birthData.birthday,
//            LatLng(
//                birthData.birthplaceLatitude,
//                birthData.birthplaceLongitude
//            ),
//            SweConst.SE_SUN,
//            birthData.isDaylightSavingTime
//        )
//
//        val calendarA = Calendar.getInstance()
//        calendarA.timeInMillis = birthData.birthday
//
//        val year = calendar.get(Calendar.YEAR) - calendarA.get(Calendar.YEAR)
//        calendarA.add(Calendar.DATE, year)
//
//        val angle2 = EphemerisUtil.getPlanetAngle(
//            context,
//            calendarA.timeInMillis,
//            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude), SweConst.SE_SUN,
//            birthData.isDaylightSavingTime
//        )
//
//        val diffResult = angle2 - angle
//
//        val horoscopeA = EphemerisUtil.calculate(
//            context,
//            Chart.SolarArc,
//            birthData.name,
//            birthData.birthday,
//            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude),
//            birthData.isDaylightSavingTime
//        )
//
//        val horoscopeB = EphemerisUtil.calculate(
//            context,
//            Chart.SolarArc,
//            birthData.name,
//            birthData.birthday,
//            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude),
//            birthData.isDaylightSavingTime
//        )
//
//        horoscopeB.getPlanetBeanList().forEach {
//            it.angle += diffResult
//        }
//        matchEvent.horoscopeA = horoscopeA
//        matchEvent.horoscopeB = horoscopeB
//        return matchEvent
//    }

    private fun calculateSolarArc(birthDateMillis: Long, currentTimeMillis: Long): Double {
        // 使用 Calendar 來設定出生日期和當前日期
        val calendar = Calendar.getInstance().apply {
            timeInMillis = currentTimeMillis
        }
        val calendarA = Calendar.getInstance().apply {
            timeInMillis = birthDateMillis
        }

        // 計算兩個日期之間的天數差距
        val daysBetween = (calendar.timeInMillis - calendarA.timeInMillis) / (1000 * 60 * 60 * 24)

        // 將天數轉換為年數 (365.25是考慮閏年的平均天數)
        val years = daysBetween / 365.25

        // 計算 Solar Arc (太陽每年大約移動1度)
        val solarArc = floor(years) // 1度每年

        return solarArc
    }

    fun getSolarArc(context: Context, birthData: BirthData, progressedTime: BirthData): MatchEvent {
        matchEvent.birthDataA = birthData
        matchEvent.chartType = Chart.SolarArc

        val solarArc = calculateSolarArc(birthData.birthday, progressedTime.birthday)

        val horoscopeA = EphemerisUtil.calculate(
            context,
            Chart.SolarArc,
            birthData.name,
            birthData.birthday,
            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude)
        )
        val horoscopeB = EphemerisUtil.calculate(
            context,
            Chart.SolarArc,
            birthData.name,
            birthData.birthday,
            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude)
        )

        matchEvent.birthDataB = birthData
        horoscopeB.birthdayTime = progressedTime.birthday
        horoscopeB.getPlanetBeanList().forEach {
            it.longitude = (it.longitude + solarArc) % 360
        }

        matchEvent.planetListA = getPlanetBeanList(context, horoscopeB, horoscopeA)
        matchEvent.houses = horoscopeA.houses
        horoscopeA.aspectList = EphemerisUtil.aspects(
            context,
            Chart.SolarArc,
            horoscopeB.planetList,
            horoscopeA.planetList,
            true,
            false
        )
        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        return matchEvent
    }

    fun getPlanetBeanList(
        context: Context,
        horoscopeA: Horoscope,
        horoscopeB: Horoscope
    ): ArrayList<PlanetBean> {
        val signList = AssetsToObjectUtil.getSignList(context)
        val planetBeanList =
            horoscopeA.getPlanetBeanList().sortedWith(compareBy {
                it.id
            })
        val planetList = planetBeanList.filter { it.isChecked }
        val list = ArrayList<PlanetBean>()
        for (planet in planetList) {
            if (planet.longitude < 0) {
                LogUtil.e("行星度數異常(小於0) ${planet.chName} ${planet.longitude}°")
                continue
            }
            val strings: ArrayList<String> = EphemerisUtil.szZodiac(planet.longitude)
            planet.signBean = SignBean(signList[strings[0].toInt()])
            planet.signBean.degree = "${strings[1]}°"
            planet.signBean.minute = strings[2]
            planet.signBean.houseData =
                EphemerisUtil.house(planet.longitude, horoscopeB.houses.cusps)
            LogUtil.i("星位 ${planet.chName} ${planet.signBean.chName} ${strings[1]}°${strings[2]} ${planet.signBean.houseData.index}宮")
            list.add(planet)
        }
        return list
    }

    fun getSynastry(
        context: Context,
        chart: Chart,
        signRecordA: BirthData,
        signRecordB: BirthData
    ): MatchEvent {
        matchEvent.birthDataA = signRecordA
        matchEvent.birthDataB = signRecordB

        val horoscopeA = EphemerisUtil.calculate(
            context,
            chart,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude)
        )

        val horoscopeB = EphemerisUtil.calculate(
            context,
            chart,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude)
        )
        horoscopeA.aspectList =
            EphemerisUtil.aspects(
                context,
                chart,
                horoscopeB.planetList,
                horoscopeA.planetList,
                true, true
            )
        horoscopeB.aspectList = horoscopeA.aspectList
        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        return matchEvent
    }

    fun getComposite(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData
    ): MatchEvent {
        val horoscopeA = EphemerisUtil.calculate(
            context,
            Chart.Composite,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude)
        )

        val horoscopeB = EphemerisUtil.calculate(
            context,
            Chart.Composite,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude)
        )

        val horoscope =
            EphemerisUtil.calculateComposite(context, Chart.Composite, horoscopeA, horoscopeB)

        matchEvent.horoscopeA = horoscope
        matchEvent.horoscopeB = horoscopeB
        return matchEvent
    }

    fun getDavison(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData
    ): MatchEvent {
        val time = (signRecordA.birthday + signRecordB.birthday) / 2
        val birthplaceLatitude =
            (signRecordA.birthplaceLatitude + signRecordB.birthplaceLatitude) / 2
        val birthplaceLongitude =
            (signRecordA.birthplaceLongitude + signRecordB.birthplaceLongitude) / 2

        val horoscopeA = EphemerisUtil.calculate(
            context,
            Chart.Davison,
            signRecordA.name + " vs " + signRecordB.name,
            time,
            LatLng(birthplaceLatitude, birthplaceLongitude)
        )
        horoscopeA.birthdayTime = signRecordA.birthday
        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = Horoscope(signRecordB)
        return matchEvent
    }

    fun getMarks(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData
    ): MatchEvent {

        val time = (signRecordA.birthday + signRecordB.birthday) / 2
        val birthplaceLatitude =
            (signRecordA.birthplaceLatitude + signRecordB.birthplaceLatitude) / 2
        val birthplaceLongitude =
            (signRecordA.birthplaceLongitude + signRecordB.birthplaceLongitude) / 2

        val timeA = (signRecordA.birthday + time) / 2
        val latitudeA = (signRecordA.birthplaceLatitude + birthplaceLatitude) / 2
        val longitudeA = (signRecordA.birthplaceLongitude + birthplaceLongitude) / 2

        val horoscopeA = EphemerisUtil.calculate(
            context,
            Chart.Marks,
            signRecordA.name,
            timeA,
            LatLng(latitudeA, longitudeA)
        )

        horoscopeA.birthdayTime = signRecordA.birthday
        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = Horoscope(signRecordB)
        return matchEvent
    }

}