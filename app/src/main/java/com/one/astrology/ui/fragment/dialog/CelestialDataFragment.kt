package com.one.astrology.ui.fragment.dialog

import android.app.AlertDialog
import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import com.afollestad.date.year
import com.google.android.gms.maps.model.LatLng
import com.google.android.material.datepicker.MaterialDatePicker
import com.jeremyliao.liveeventbus.LiveEventBus
import com.loper7.date_time_picker.DateTimeConfig
import com.loper7.date_time_picker.dialog.CardDatePickerDialog
import com.one.astrology.R
import com.one.astrology.constant.KeyDefine
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentCelestialDataBinding
import com.one.astrology.event.EventKey
import com.one.astrology.event.LatLngEvent
import com.one.astrology.ui.activity.MapActivity
import com.one.astrology.ui.activity.SignDetailActivity
import com.one.astrology.ui.fragment.viewmodel.SharedViewModel
import com.one.astrology.util.LocationUtil
import com.one.astrology.util.astro.ChartUtils.toStorageValue
import com.one.astrology.util.launchWhenStarted
import com.one.core.util.FormatUtils
import com.one.core.util.InputTools
import com.one.core.util.LogUtil
import java.util.Calendar
import java.util.Date

/**
 * 出生資料頁
 */
class CelestialDataFragment : DialogFragment(R.layout.fragment_birth_data_dialog) {

    private var mDate: Date? = null
    private lateinit var materialDatePicker: MaterialDatePicker<Long>
    private var birthData: BirthData = BirthData()
    private var latitude = 25.047998
    private var longitude = 121.554483
    private var placeType = 0
    private lateinit var binding: FragmentCelestialDataBinding
    private lateinit var dialog: AlertDialog
    private val calendar = Calendar.getInstance()
    private val sharedViewModel by viewModels<SharedViewModel>()

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val builder = AlertDialog.Builder(requireActivity())
        val inflater = requireActivity().layoutInflater
        binding = FragmentCelestialDataBinding.inflate(inflater, null, false)
        builder.setView(binding.root)

        birthData = BirthData()
        birthData.name = requireActivity().getString(Chart.Celestial.type)
        birthData.birthplaceLatitude = latitude
        birthData.birthplaceLongitude = longitude

        initView()
        initDatePicker()
        val latLng = LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude)
        toAddresses(latLng)

        dialog = builder.create()
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
        return dialog
    }

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("天象盤資訊頁", this.javaClass.simpleName)
    }

    override fun onDestroy() {
        super.onDestroy()
        // Remove LiveEventBus observers to prevent memory leaks
        LiveEventBus.get(EventKey.LatLng, LatLngEvent::class.java).removeObserver({})
    }

    fun initView() {
        binding.tvBirthPlace.setOnClickListener {
            placeType = 1
            val bundle = Bundle()
            bundle.putDouble(KeyDefine.Latitude, birthData.birthplaceLatitude)
            bundle.putDouble(KeyDefine.Longitude, birthData.birthplaceLongitude)
            startActivity(MapActivity::class.java, bundle)
        }

        binding.tvDate.setOnClickListener {
            val calendar = Calendar.getInstance()
            calendar.year = calendar[Calendar.YEAR] - 20

            if (mDate != null) {
                calendar.time = mDate!!
            }
            val list = intArrayOf(
                DateTimeConfig.YEAR,
                DateTimeConfig.MONTH,
                DateTimeConfig.DAY,
                DateTimeConfig.HOUR,
                DateTimeConfig.MIN
            ).toMutableList()

            CardDatePickerDialog.builder(requireContext())
                .setTitle("選擇出生日期與時間")
                .setBackGroundModel(CardDatePickerDialog.CARD)
                .setDefaultTime(calendar.timeInMillis)
                .setDisplayType(list)
                .setLabelText("年", "月", "日", "時", "分")
                .showDateLabel(true)
                .showFocusDateInfo(true)
                .showBackNow(true)
                .setOnChoose("選擇") { millisecond ->
                    val timeString: String =
                        FormatUtils.dateToString(Date(millisecond), "yyyy/MM/dd HH:mm")
                    binding.tvDate.setText(timeString)
                    mDate = Date(millisecond)
                }
                .setOnCancel("取消") { }
                .build().show()
        }

        binding.btSave.setOnClickListener {
            launchWhenStarted {
                addData(it)
            }
        }
        latLngEvent()
    }

    private fun latLngEvent() {
        LiveEventBus.get(EventKey.LatLng, LatLngEvent::class.java)
            .observeStickyForever { latLngEvent: LatLngEvent? ->
                if (latLngEvent != null) {
                    birthData.birthplaceLatitude = latLngEvent.center.latitude
                    birthData.birthplaceLongitude = latLngEvent.center.longitude
                    val text = "$latitude , $longitude"
                    LogUtil.d("經緯度 ：$text")
                    toAddresses(latLngEvent.center)
                }
            }
    }

    private fun toAddresses(center: LatLng) {
        launchWhenStarted {
            if (!isAdded) {
                return@launchWhenStarted
            }
            val addressString =
                LocationUtil.latLngToAddresses(center, listOf("city"), "")
            birthData.birthplaceLatitude = center.latitude
            birthData.birthplaceLongitude = center.longitude

            binding.tvBirthPlace.setText(addressString)
        }
    }

    private suspend fun addData(view: View) {
        if (TextUtils.isEmpty(binding.tvDate.text.toString()) || latitude == -1.0 || longitude == -1.0) {
            InputTools.HideKeyboard(view)
            Toast.makeText(
                requireActivity(),
                getString(R.string.please_fill_in_the_information),
                Toast.LENGTH_SHORT
            ).show()
            return
        }

        setData()

        // Update ViewModel before dismissing dialog
        if (isAdded && !isDetached) {
            sharedViewModel.data.postValue("")
        }

        dialog.dismiss()
        toSignDetailActivity()
    }

    private suspend fun setData() {
        birthData.name = getString(Chart.Celestial.type)
        birthData.createTime = System.currentTimeMillis()

        val strTime = binding.tvDate.text.toString()
        val date = FormatUtils.stringToDate(strTime, "yyyy/MM/dd HH:mm")
        calendar.timeInMillis = date.time
        birthData.birthday = calendar.timeInMillis
        mDate = calendar.time

        birthData.birthdayString =
            FormatUtils.longToString(birthData.birthday, "yyyy/MM/dd HH:mm")

        birthData.birthplaceArea = LocationUtil.latLngToArea(
            LatLng(
                birthData.birthplaceLatitude,
                birthData.birthplaceLongitude
            )
        )
    }

    private fun toSignDetailActivity() {
        if (!isAdded || isDetached) {
            return
        }
        val bundle = Bundle()
        bundle.putString(KeyDefine.Chart, Chart.Celestial.toStorageValue())
        bundle.putParcelable(KeyDefine.UserBirthData, birthData)
        startActivity(SignDetailActivity::class.java, bundle)
    }

    private fun startActivity(clz: Class<*>?, bundle: Bundle?) {
        if (isAdded) {  // 確保 Fragment 已經附加到 Activity
            val intent = Intent()
            intent.setClass(requireContext(), clz!!)
            if (bundle != null) {
                intent.putExtras(bundle)
            }
            startActivity(intent)
        }
    }


    private fun initDatePicker() {
        val calendar = Calendar.getInstance()
        mDate = Date(calendar.timeInMillis)
        val timeString: String = FormatUtils.dateToString(mDate, "yyyy/MM/dd HH:mm")
        binding.tvDate.setText(timeString)

        materialDatePicker =
            MaterialDatePicker.Builder.datePicker()
                .setTitleText("選擇出生日期")
                .setSelection(calendar.timeInMillis)
//                .setInputMode(MaterialDatePicker.INPUT_MODE_TEXT)
                .build()

        materialDatePicker.addOnPositiveButtonClickListener {
            val time: String = FormatUtils.dateToString(Date(it), "yyyy/MM/dd")
            binding.tvDate.setText(time)
        }
    }
}