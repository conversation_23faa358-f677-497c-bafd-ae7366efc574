package com.one.astrology.ui.fragment.viewmodel.calculate

import android.content.Context
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.ReceptionData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.event.EventKey
import com.one.astrology.event.MatchEvent
import com.one.core.util.LogUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
open class CalculateViewModel @Inject constructor(
) : ProgressionViewModel() {

    private val _featureList = MutableStateFlow<List<ReceptionData>>(emptyList())
    val featureList: StateFlow<List<ReceptionData>> = _featureList.asStateFlow()

    private val _arabicPlanetList = MutableStateFlow<List<PlanetBean>>(emptyList())
    val arabicPlanetList: StateFlow<List<PlanetBean>> = _arabicPlanetList

    private val _calculationState = MutableStateFlow<CalculationState>(CalculationState.Idle)
    val calculationState = _calculationState.asStateFlow()

    fun updateArabicPlanetList(list: List<PlanetBean>) {
        _arabicPlanetList.value = list
    }

    fun updateFeatureList(list: List<ReceptionData>) {
        _featureList.value = list
    }

    suspend fun doSingle(
        context: Context,
        birthData: BirthData,
        chart: Chart
    ): MatchEvent = withContext(Dispatchers.Default) {
        try {
            _loading.emit(true)
            _calculationState.emit(CalculationState.Calculating)

            matchEvent = MatchEvent(birthData, chart)
            matchEvent.isSingle = true

            matchEvent = when (chart) {
                Chart.Celestial -> {
                    getBasic(context, chart, birthData)
                }

                Chart.Natal -> {
                    getBasic(context, chart, birthData)
                }

                else -> {
                    getBasic(context, chart, birthData)
                }
            }

            // 發送事件通知
            withContext(Dispatchers.Main) {
                LiveEventBus.get<MatchEvent>(EventKey.MatchEvent).postDelay(matchEvent, 0)
            }

            _calculationState.emit(CalculationState.Success)

            matchEvent
        } catch (e: Exception) {
            LogUtil.e("計算失敗 " + e.message)
            _calculationState.emit(CalculationState.Error(e.message ?: "未知錯誤"))
            throw e
        } finally {
            _loading.emit(false)
        }
    }

    suspend fun doDouble(
        context: Context,
        birthDataA: BirthData,
        birthDataB: BirthData,
        progressedTime: BirthData,
        chart: Chart
    ): MatchEvent = withContext(Dispatchers.Default) {
        try {
            _loading.emit(true)
            _calculationState.emit(CalculationState.Calculating)

            matchEvent = MatchEvent(birthDataA, birthDataB, chart)
            matchEvent.isSingle = false

            matchEvent = when (chart) {
                Chart.SecondaryProgression -> {
                    getSecondaryProgression(context, birthDataA, birthDataB)
                }

                Chart.TertiaryProgression -> {
                    getTertiaryProgression(context, birthDataA, progressedTime)
                }

                Chart.SolarReturn -> {
                    matchEvent.isSingle = true
                    getSolarReturn(context, birthDataA, progressedTime)
                }

                Chart.LunarReturn -> {
                    matchEvent.isSingle = true
                    getLunarReturn(context, birthDataA, progressedTime)
                }

                Chart.SolarArc -> {
                    getSolarArc(context, birthDataA, progressedTime)
                }

                Chart.SecondaryProgressionSynastry -> {
                    getSecondaryProgressionSynastry(context, birthDataA, birthDataB, progressedTime)
                }

                Chart.TertiaryProgressionSynastry -> {
                    getTertiaryProgressionSynastry(context, birthDataA, birthDataB, progressedTime)
                }

                Chart.Synastry -> {
                    getSynastry(context, chart, birthDataA, birthDataB)
                }

                Chart.SynastrySecondaryProgression -> {
                    matchEvent.isSingle = true
                    getSynastrySecondary(context, birthDataA, birthDataB, progressedTime)
                }

                Chart.SynastryTertiaryProgression -> {
                    matchEvent.isSingle = true
                    getSynastryTertiary(context, birthDataA, birthDataB, progressedTime)
                }

                Chart.Composite -> {
                    getComposite(context, birthDataA, birthDataB)
                }

                Chart.CompositeSecondaryProgression -> {
                    matchEvent.isSingle = true
                    getCompositeSecondary(context, birthDataA, birthDataB, progressedTime)
                }

                Chart.CompositeTertiaryProgression -> {
                    matchEvent.isSingle = true
                    getCompositeTertiary(context, birthDataA, birthDataB, progressedTime)
                }

                Chart.Davison -> {
                    getDavison(context, birthDataA, birthDataB)
                }

                Chart.DavisonSecondaryProgression -> {
                    matchEvent.isSingle = true
                    getDavisonSecondary(context, birthDataA, birthDataB, progressedTime)
                }

                Chart.DavisonTertiaryProgression -> {
                    matchEvent.isSingle = true
                    getDavisonTertiary(context, birthDataA, birthDataB, progressedTime)
                }

                Chart.Marks -> {
                    getMarks(context, birthDataA, birthDataB)
                }

                Chart.MarksSecondaryProgression -> {
                    matchEvent.isSingle = true
                    getMarksSecondary(context, birthDataA, birthDataB, progressedTime)
                }

                Chart.MarksTertiaryProgression -> {
                    matchEvent.isSingle = true
                    getMarksTertiary(context, birthDataA, birthDataB, progressedTime)
                }

                Chart.Transit -> {
                    getSynastry(context, chart, birthDataA, progressedTime)
                }

                Chart.Firdaria -> {
                    getFirdaria(context, birthDataA, progressedTime)
                }

                else -> {
                    getSynastry(context, chart, birthDataA, birthDataB)
                }
            }

            // 發送事件通知
            withContext(Dispatchers.Main) {
                LiveEventBus.get<MatchEvent>(EventKey.MatchEvent).postDelay(matchEvent, 0)
            }

            _calculationState.emit(CalculationState.Success)
            matchEvent
        } catch (e: Exception) {
            LogUtil.e("計算失敗 " + e.message)
            _calculationState.emit(CalculationState.Error(e.message ?: "未知錯誤"))
            throw e
        } finally {
            _loading.emit(false)
        }
    }
}

sealed class CalculationState {
    object Idle : CalculationState()
    object Calculating : CalculationState()
    object Success : CalculationState()
    data class Error(val message: String) : CalculationState()
}