package com.one.astrology.ui.fragment.bottomSheet

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import com.afollestad.materialdialogs.MaterialDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.journeyapps.barcodescanner.ScanContract
import com.journeyapps.barcodescanner.ScanIntentResult
import com.journeyapps.barcodescanner.ScanOptions
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.entity.BirthData
import com.one.astrology.databinding.FragmentSettingBottomSheetBinding
import com.one.astrology.event.AddSignRecordEvent
import com.one.astrology.event.EventKey
import com.one.astrology.ui.activity.SettingsComposeActivity
import com.one.astrology.util.AESUtils
import com.one.core.dialog.AboutDialogFragment
import com.one.core.dialog.DonateDialogFragment
import com.one.core.util.LogUtil

/**
 * SettingBottomSheetFragment
 */
class SettingBottomSheetFragment : BottomSheetDialogFragment(R.layout.fragment_setting_bottom_sheet) {

    private lateinit var binding: FragmentSettingBottomSheetBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentSettingBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("SettingBottomSheetFragment", this.javaClass.simpleName)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Set up click listeners for both the card and the text view
        binding.cardBilling.setOnClickListener {
            DonateDialogFragment().show(childFragmentManager, "")
        }
        binding.tvBilling.setOnClickListener {
            DonateDialogFragment().show(childFragmentManager, "")
        }

        binding.cardScan.setOnClickListener {
            barcodeLauncher.launch(ScanOptions())
            dismiss()
        }
        binding.tvScan.setOnClickListener {
            barcodeLauncher.launch(ScanOptions())
            dismiss()
        }

        binding.cardParameters.setOnClickListener {
            startActivity(Intent(requireActivity(), SettingsComposeActivity::class.java))
            dismiss()
        }
        binding.tvParameters.setOnClickListener {
            startActivity(Intent(requireActivity(), SettingsComposeActivity::class.java))
            dismiss()
        }

        binding.cardShare.setOnClickListener {
            shareApp()
        }
        binding.tvShare.setOnClickListener {
            shareApp()
        }

        binding.cardAbout.setOnClickListener {
            AboutDialogFragment().show(requireActivity().supportFragmentManager, "")
        }
        binding.tvAbout.setOnClickListener {
            AboutDialogFragment().show(requireActivity().supportFragmentManager, "")
        }
    }

    private fun shareApp() {
        val appName = getString(R.string.app_name)
        val intentShare = Intent(Intent.ACTION_SEND)
        intentShare.type = "text/plain"
        intentShare.putExtra(Intent.EXTRA_SUBJECT, "分享")
        intentShare.putExtra(
            Intent.EXTRA_TEXT,
            "$appName\nhttps://play.google.com/store/apps/details?id=${requireActivity().packageName}"
        )
        startActivity(Intent.createChooser(intentShare, appName))
        dismiss()
    }

    // Register the launcher and result handler
    private val barcodeLauncher: ActivityResultLauncher<ScanOptions?> = registerForActivityResult(
        ScanContract()
    ) { result: ScanIntentResult ->
        if (result.contents == null) {
            Toast.makeText(requireContext(), "取消", Toast.LENGTH_LONG).show()
        } else {
            val gson = Gson()
            val contents: String = AESUtils.decrypt(result.contents)
            val birthDataItem: BirthData = gson.fromJson(contents, BirthData::class.java)
            val dialog = MaterialDialog(requireActivity(), MaterialDialog.DEFAULT_BEHAVIOR)
            dialog.title(null, "是否要加入此筆記錄?")
            dialog.message(null, birthDataItem.name, null)
            dialog.positiveButton(null, "是") { _: MaterialDialog? ->
                dialog.dismiss()
                try {
                    val birthData = BirthData(birthDataItem)
                    val itemBoxSignRecord = ObjectBox.get().boxFor(BirthData::class.java)
                    itemBoxSignRecord?.put(birthData)
                    LogUtil.d("Scanned: " + result.contents)
                    LiveEventBus.get<AddSignRecordEvent>(EventKey.AddUserBirthData)
                        .post(AddSignRecordEvent(birthData))
                } catch (e: Exception) {
                    e.printStackTrace()
                    e.message?.let { LogUtil.e(it) }
                }
            }
            dialog.negativeButton(null, "否") {
                dialog.dismiss()
            }
            dialog.show()
        }
    }
}