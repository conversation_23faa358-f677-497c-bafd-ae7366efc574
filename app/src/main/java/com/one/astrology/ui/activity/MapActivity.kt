package com.one.astrology.ui.activity


import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.os.Bundle
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationServices
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.GoogleMap.OnMarkerClickListener
import com.google.android.gms.maps.MapsInitializer
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.BitmapDescriptor
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.Marker
import com.google.android.gms.maps.model.MarkerOptions
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.R
import com.one.astrology.constant.KeyDefine
import com.one.astrology.databinding.ActivityMapBinding
import com.one.astrology.event.EventKey
import com.one.astrology.event.LatLngEvent
import com.one.astrology.util.LocationUtil
import com.one.core.util.LogUtil
import com.one.core.view.LoadingDialog
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import kotlin.math.abs

@AndroidEntryPoint
class MapActivity : PermissionActivity(), OnMarkerClickListener, OnMapReadyCallback {

    private var center: LatLng = LatLng(25.047998, 121.554483)
    private var mLoadingDialog: LoadingDialog? = null
    private var mMap: GoogleMap? = null
    private var lastKnownLocation: LatLng? = null
    private val defaultLocation = LatLng(25.047998, 121.554483)
    private var mFusedLocationProviderClient: FusedLocationProviderClient? = null
    private var centerMarker: Marker? = null
    private lateinit var binding: ActivityMapBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        checkPermission(true)
        super.onCreate(savedInstanceState)
        MapsInitializer.initialize(applicationContext)

        binding = ActivityMapBinding.inflate(layoutInflater)
        setContentView(binding.root)

        mFusedLocationProviderClient =
            LocationServices.getFusedLocationProviderClient(appCompatActivity)
        mLoadingDialog = LoadingDialog(appCompatActivity)

        if (isAllowPermissions) {
            val mapFragment =
                supportFragmentManager.findFragmentById(R.id.map) as SupportMapFragment?
            mapFragment?.getMapAsync(this)
        }


        initView()
        initArrayAdapter()
    }

    private fun initView() {
        binding.tvSearchPlace.setOnEditorActionListener { v, actionId, event ->

            lifecycleScope.launch {
                val latLng = LocationUtil.addressToLatLng(this@MapActivity, v?.text.toString())
                if (mMap != null) {
                    mMap?.moveCamera(
                        CameraUpdateFactory.newLatLngZoom(
                            latLng,
                            DEFAULT_ZOOM
                        )
                    )
                }
                updateLatLng(latLng)
                binding.tvSearchPlace.clearFocus()
                binding.bottomSheet.tvAddress.requestFocus()
                hideKeyboardFromView(this@MapActivity, binding.tvSearchPlace)
            }
            false
        }

        binding.bottomSheet.tvLongitude.setOnEditorActionListener { v, actionId, event ->
            checkLatLng()
            binding.bottomSheet.tvLongitude.clearFocus()
            binding.bottomSheet.tvAddress.requestFocus()
            hideKeyboardFromView(this, binding.bottomSheet.tvLongitude)
            false
        }

        binding.bottomSheet.tvLatitude.setOnEditorActionListener { v, actionId, event ->
            checkLatLng()

            binding.bottomSheet.tvLatitude.clearFocus()
            binding.bottomSheet.tvAddress.requestFocus()
            hideKeyboardFromView(this, binding.bottomSheet.tvLatitude)
            false
        }

        binding.bottomSheet.tvAddress.setOnClickListener {
            lifecycleScope.launch {
                val addressString = LocationUtil.latLngToAddresses(center)
                binding.bottomSheet.tvAddress.text = addressString
            }
        }

        binding.bottomSheet.tvOK.setOnClickListener {
            if (mMap == null) {
                return@setOnClickListener
            }
            val latLng = getLatLng()
            LiveEventBus.get<LatLngEvent>(EventKey.LatLng).post(LatLngEvent(latLng))
            finish()
        }

        binding.btMyLocation.setOnClickListener {
            deviceLocation
        }
    }

    private fun initArrayAdapter() {
        ArrayAdapter.createFromResource(
            this,
            R.array.latitude,
            R.layout.spinner_item_latlag
        ).also { adapter ->
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding.bottomSheet.spLatitude.adapter = adapter
        }
        ArrayAdapter.createFromResource(
            this,
            R.array.longitude,
            R.layout.spinner_item_latlag
        ).also { adapter ->
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding.bottomSheet.spLongitude.adapter = adapter
        }
        binding.bottomSheet.spLatitude.isEnabled = false
        binding.bottomSheet.spLatitude.onItemSelectedListener =
            object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(
                    parent: AdapterView<*>?,
                    view: View?,
                    position: Int,
                    id: Long
                ) {
                    val result: String = parent?.getItemAtPosition(position).toString()
                    LogUtil.d(result)
                    checkLatLng()
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {

                }
            }
        binding.bottomSheet.spLongitude.isEnabled = false
        binding.bottomSheet.spLongitude.onItemSelectedListener =
            object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(
                    parent: AdapterView<*>?,
                    view: View?,
                    position: Int,
                    id: Long
                ) {
                    val result: String = parent?.getItemAtPosition(position).toString()
                    LogUtil.d(result)
                    checkLatLng()
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {

                }
            }
    }

    private fun checkLatLng() {
        center = getLatLng()

        if (mMap != null) {
            mMap?.moveCamera(CameraUpdateFactory.newLatLngZoom(center, DEFAULT_ZOOM))
            updateLatLng(center)
        }
    }

    private fun hideKeyboardFromView(context: Context, view: View) {
        val inputMethodManager =
            context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        inputMethodManager.hideSoftInputFromWindow(view.windowToken, 0)
    }

    private fun updateLatLng(latLng: LatLng) {
        setLatLogText()
        addMyLocationMarker()
        lifecycleScope.launch {
            val addressString = LocationUtil.latLngToAddresses(latLng)
            binding.bottomSheet.tvAddress.text = addressString
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (isAllowPermissions) {
            val mapFragment =
                supportFragmentManager.findFragmentById(R.id.map) as SupportMapFragment?
            mapFragment?.getMapAsync(this)
        }
    }

    override fun initParams(bundle: Bundle?) {}

    override fun onMarkerClick(marker: Marker): Boolean {
        return false
    }

    override fun onMapReady(googleMap: GoogleMap) {
        mMap = googleMap
        if (ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_COARSE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return
        }
        mMap?.isMyLocationEnabled = false
        mMap?.uiSettings?.isMyLocationButtonEnabled = false

        // When Map Loads Successfully
        mMap?.setOnMapLoadedCallback {
//            setLatLogText()
            addMyLocationMarker()
            lifecycleScope.launch {
                val addressString = LocationUtil.latLngToAddresses(center)
                binding.bottomSheet.tvAddress.text = addressString
            }
        }
        mMap?.setOnCameraMoveListener {
            setLatLogText()
            addMyLocationMarker()
        }
        val latitude = intent.getDoubleExtra(KeyDefine.Latitude, -1.0)
        val longitude = intent.getDoubleExtra(KeyDefine.Longitude, -1.0)
        if (latitude != -1.0 && longitude != -1.0) {
            val latLng = LatLng(latitude, longitude)
            mMap?.moveCamera(CameraUpdateFactory.newLatLngZoom(latLng, DEFAULT_ZOOM))
        } else {
            deviceLocation
        }
    }

    private fun setLatLogText() {
        val latLngBounds = mMap?.projection?.visibleRegion?.latLngBounds
        if (latLngBounds != null) {
            center = latLngBounds.center
        }
        convertToDMS(center.latitude, center.longitude)
        binding.bottomSheet.tvAddress.text = "點擊更新地址"
        binding.bottomSheet.tvBirthPlace.setText("")
    }

    private fun getLatLng(): LatLng {
        var latDirection = "N"
        if (binding.bottomSheet.spLatitude.selectedItemPosition == 1) {
            latDirection = "S"
        }
        var lonDirection = "E"
        if (binding.bottomSheet.spLongitude.selectedItemPosition == 1) {
            lonDirection = "W"
        }

        val lon = binding.bottomSheet.tvLongitude.text.toString()
        val lat = binding.bottomSheet.tvLatitude.text.toString()
        if (!lon.isNullOrEmpty() && !lat.isNullOrEmpty()) {
            val lonDegrees = lon.split("°")[0].safeToInt()
            val lonMinutes = lon.split("°")[1].split("′")[0].safeToInt()
            val lonSeconds = parseNumber(lon.split("°")[1].split("′")[1])
            val longitude = convertToDecimal(lonDegrees, lonMinutes, lonSeconds, lonDirection)

            val latDegrees = lat.split("°")[0].safeToInt()
            val latMinutes = lat.split("°")[1].split("′")[0].safeToInt()
            val latSeconds = parseNumber(lat.split("°")[1].split("′")[1])
            val latitude = convertToDecimal(latDegrees, latMinutes, latSeconds, latDirection)
            val result =
                "Latitude: $latDegrees°${latMinutes}′${"%.2f".format(latSeconds)}″ $latDirection, " +
                        "Longitude: $lonDegrees°${lonMinutes}′${"%.2f".format(lonSeconds)}″ $lonDirection"
            LogUtil.d(result)
            LogUtil.d("longitude $longitude  latitude $latitude")
            return LatLng(latitude, longitude)
        }

        return defaultLocation
    }

    private fun String?.safeToInt(): Int {
        return if (this.isNullOrEmpty()) {
            0
        } else {
            try {
                this.replace("″", "").toInt()
            } catch (e: NumberFormatException) {
                println("Invalid number format: $this")
                0
            }
        }
    }


    private fun parseNumber(input: String?): Double {
        return if (input.isNullOrEmpty()) {
            // 給一個默認值或拋出自定義異常
            0.0
        } else {
            try {
                input.replace("″", "").toDouble()
            } catch (e: NumberFormatException) {
                // 處理數字格式錯誤
                println("Invalid number format: $input")
                0.0 // 默認值
            }
        }
    }

    private fun convertToDecimal(
        degrees: Int,
        minutes: Int,
        seconds: Double,
        direction: String
    ): Double {
        // 將度、分、秒轉為十進制
        val decimal = degrees + minutes / 60.0 + seconds / 3600.0
        // 根據方向轉換正負值
        return if (direction == "S" || direction == "W") -decimal else decimal
    }

    private fun convertToDMS(latitude: Double, longitude: Double): String {
        // 緯度轉換
        val latDirection = if (latitude >= 0) "N" else "S"
        val latAbs = abs(latitude)
        val latDegrees = latAbs.toInt()
        val latMinutes = ((latAbs - latDegrees) * 60).toInt()
        val latSeconds = ((latAbs - latDegrees) * 60 - latMinutes) * 60

        // 經度轉換
        val lonDirection = if (longitude >= 0) "E" else "W"
        val lonAbs = abs(longitude)
        val lonDegrees = lonAbs.toInt()
        val lonMinutes = ((lonAbs - lonDegrees) * 60).toInt()
        val lonSeconds = ((lonAbs - lonDegrees) * 60 - lonMinutes) * 60

        if (latitude >= 0) {
            // 北緯（N）
            binding.bottomSheet.spLatitude.setSelection(0)
        } else {
            // 南緯（S）
            binding.bottomSheet.spLatitude.setSelection(1)
        }
        if (longitude >= 0) {
            // 東經（E）
            binding.bottomSheet.spLongitude.setSelection(0)
        } else {
            // 西經（W）
            binding.bottomSheet.spLongitude.setSelection(1)
        }
        val lat = "$latDegrees°${latMinutes}′${"%.2f".format(latSeconds)}″"
        binding.bottomSheet.tvLatitude.text = lat
        val lon = "$lonDegrees°${lonMinutes}′${"%.2f".format(lonSeconds)}″"
        binding.bottomSheet.tvLongitude.text = lon
        val result =
            "Latitude: $latDegrees°${latMinutes}′${"%.2f".format(latSeconds)}″ $latDirection, " +
                    "Longitude: $lonDegrees°${lonMinutes}′${"%.2f".format(lonSeconds)}″ $lonDirection"
        LogUtil.d(result)
        return result
    }

    private fun addMyLocationMarker() {

        val latLngBounds = mMap?.projection?.visibleRegion?.latLngBounds
        if (latLngBounds != null) {
            center = latLngBounds.center
        }
        val makerOptions = MarkerOptions()
        makerOptions.position(center)
        makerOptions.draggable(true)
        makerOptions.visible(true)
        makerOptions.icon(bitmapDescriptorFromVector(R.drawable.ic_baseline_location_on_72))

        if (centerMarker == null) {
            centerMarker = mMap?.addMarker(makerOptions)
        } else {
            centerMarker?.position = center
        }
        centerMarker?.showInfoWindow()
    }

    private fun bitmapDescriptorFromVector(vectorResId: Int): BitmapDescriptor {
        val vectorDrawable = ContextCompat.getDrawable(appCompatActivity, vectorResId)
        vectorDrawable?.setBounds(
            0,
            0,
            vectorDrawable.intrinsicWidth,
            vectorDrawable.intrinsicHeight
        )
        val bitmap = Bitmap.createBitmap(
            vectorDrawable?.intrinsicWidth ?: 0,
            vectorDrawable?.intrinsicHeight ?: 0,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(bitmap)
        vectorDrawable?.draw(canvas)
        return BitmapDescriptorFactory.fromBitmap(bitmap)
    }

    private val deviceLocation: Unit
        get() {
            try {
                mFusedLocationProviderClient?.lastLocation?.addOnSuccessListener(
                    appCompatActivity
                ) { location ->
                    if (location != null) {
                        lastKnownLocation = LatLng(location.latitude, location.longitude)
                        mMap?.moveCamera(
                            CameraUpdateFactory.newLatLngZoom(
                                lastKnownLocation!!,
                                DEFAULT_ZOOM
                            )
                        )
                        mMap?.animateCamera(CameraUpdateFactory.zoomTo(DEFAULT_ZOOM))
                        LogUtil.d("LastKnownLocation : " + lastKnownLocation?.latitude + " , " + lastKnownLocation?.longitude)
                    } else {
                        mMap?.moveCamera(
                            CameraUpdateFactory.newLatLngZoom(
                                defaultLocation,
                                DEFAULT_ZOOM
                            )
                        )
                        mMap?.uiSettings?.isMyLocationButtonEnabled = false
                        Toast.makeText(
                            appCompatActivity,
                            "Location is null",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            } catch (e: SecurityException) {
                e.message?.let { LogUtil.e(it) }
            }
        }

    companion object {
        private const val DEFAULT_ZOOM = 13f
        fun createBundle(latitude: Double, longitude: Double): Bundle {
            return Bundle().apply {
                putDouble(KeyDefine.Latitude, latitude)
                putDouble(KeyDefine.Longitude, longitude)
            }
        }
    }
}