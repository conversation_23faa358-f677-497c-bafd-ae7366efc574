package com.one.astrology.data.bean

import android.content.Context
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.constant.PrefKey
import com.one.astrology.data.db.SettingAspectData
import com.one.astrology.data.db.SettingAspectData_
import com.one.astrology.data.type.Chart
import com.one.core.util.PrefUtils
import java.io.Serializable

/**
 * 相位
 */
class Aspect(
    var type: AspectType,
    var planet: PlanetBean,
    var isDraw: Boolean,
    var direction: String? = null
) : Serializable {
    var deltaDegree = 0
    var deltaMinute = 0
    var orb: Double? = 0.0

    companion object {
        // 相位類型常量
        const val CONJUNCTION = 0      // 合相
        const val SEXTILE = 60         // 六分相
        const val SQUARE = 90          // 刑相
        const val TRINE = 120          // 拱相
        const val OPPOSITION = 180     // 沖相
        const val SEMI_SEXTILE = 30    // 半六分相
        const val SEMI_SQUARE = 45     // 半刑相
        const val SESQUI_SQUARE = 135  // 倍半刑相
        const val QUINCUNX = 150       // 偏相
        const val NONE = -1            // 無相位
    }
}

enum class AspectType(
    val value: Int,
    var orb: Float,
    val nameCh: String,
    val color: Int,
    val unicode: String,
    var isOpen: Boolean = true,
) : Serializable {
    Conjunction(0, 6F, "合", R.color.conjunction, "☌"), // //Color.parseColor("#6184A4")
    SemiSextile(30, 2F, "半六合", R.color.angle_30, ""), // Color.parseColor("#4D1F4D1B")),
    SemiSquare(45, 2F, "八分", R.color.angle_45, ""), //Color.parseColor("#73FF0000")),
    Sextile(60, 3F, "六合", R.color.sextile, "✶"), //Color.parseColor("#47B4B5")),
    Quintile(72, 2F, "五分", R.color.angle_72, ""), //Color.parseColor("#000000")),
    Square(90, 5F, "刑", R.color.square, "□"), //Color.parseColor("#FF0000")),
    Trine(120, 5F, "拱", R.color.trine, "△"), //Color.parseColor("#1F4D1B")),
    Sesquiquadrate(135, 2F, "補八分", R.color.angle_135, ""), //Color.parseColor("#4DFF0000")),
    Biquintile(144, 2F, "倍五分", R.color.angle_144, ""), //Color.parseColor("#000000")),
    Quincunx(150, 2F, "梅花", R.color.angle_150, ""), //Color.parseColor("#99FF0000")),
    Opposition(180, 5F, "衝", R.color.opposition, "☍"); //Color.parseColor("#0A0AFF"));

    companion object {

        private fun getOrb(context: Context, key: String?, defaultValue: String?): Float {
            return PrefUtils.getFromPrefs(context, key, defaultValue)?.toInt()?.toFloat() ?: 0F
        }

//        private fun getOrb(context: Context, key: String, degree: Int): Float {
//            return DBHelper.querySettingAspect(context, key, degree).orb.toFloat()
//        }

        private fun getAspectData(
            chart: Chart,
            degree: Int
        ): SettingAspectData? {
            val itemBox = ObjectBox.get().boxFor(SettingAspectData::class.java)
            return itemBox.query(
                SettingAspectData_.enName.equal(chart.nameEng).and(
                    SettingAspectData_.degree.equal(
                        degree
                    )
                )
            ).build().findFirst()
        }

        fun getType(context: Context, angle: Double): AspectType? {
            for (type in values()) {
                when (type.value) {
                    0 -> type.orb = getOrb(context, PrefKey.KEY_DEGREE_0, "6")

                    60 -> type.orb = getOrb(context, PrefKey.KEY_DEGREE_60, "3")

                    90 -> type.orb = getOrb(context, PrefKey.KEY_DEGREE_90, "5")

                    120 -> type.orb = getOrb(context, PrefKey.KEY_DEGREE_120, "5")

                    180 -> type.orb = getOrb(context, PrefKey.KEY_DEGREE_180, "5")
                }
                if (type.value - type.orb <= angle && angle <= type.value + type.orb) {
                    return type
                }
            }
            return null
        }

        fun getType(angle: Double): AspectType? {
            for (type in values()) {
                type.orb = 0f
                if (type.value - type.orb <= angle && angle <= type.value + type.orb) {
                    return type
                }
            }
            return null
        }

        fun getType(chart: Chart, angle: Double): AspectType? {
            for (type in values()) {
                val it = getAspectData(chart, type.value)
//                when (type.value) {
//                    0 -> it = getAspectData(chart, 0)
//                    30 -> it = getAspectData(chart, 30)
//                    45 -> it = getAspectData(chart, 45)
//                    60 -> it = getAspectData(chart, 60)
//
//                    90 -> it = getAspectData(chart, 90)
//
//                    120 -> it = getAspectData(chart, 120)
//
//                    180 -> it = getAspectData(chart, 180)
//                }
                if (it != null) {
                    type.orb = it.orb?.toFloat() ?: 0F
                }
                if (it != null) {
                    type.isOpen = it.isOpen == true
                }
                if (type.value - type.orb <= angle && angle <= type.value + type.orb) {
                    return type
                }
            }
            return null
        }
    }
}