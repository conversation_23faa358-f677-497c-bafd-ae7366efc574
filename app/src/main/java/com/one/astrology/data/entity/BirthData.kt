package com.one.astrology.data.entity

import android.content.Context
import android.os.Parcel
import android.os.Parcelable
import com.google.android.gms.maps.model.LatLng
import com.google.gson.annotations.SerializedName
import com.one.astrology.data.BaseCheck
import com.one.astrology.data.Horoscope
import com.one.astrology.data.type.Chart
import com.one.astrology.util.EphemerisUtil
import com.one.core.util.LogUtil
import io.objectbox.BoxStore
import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

@Entity
class BirthData() : BaseCheck(), Parcelable {
    @Id(assignable = true)
    var id: Long = 0

    @SerializedName("createTime")
    var createTime: Long = 0

    @SerializedName("name")
    var name: String = ""

    @SerializedName("birthday")
    var birthday: Long = 0

    @SerializedName("isDaylightSavingTime")
    var isDaylightSavingTime: Boolean = false

    @SerializedName("timezoneOffset")
    var timezoneOffset: Double = 0.0  // 時區偏移量（小時）

    var birthdayString: String? = ""

    @SerializedName("birthplace_latitude")
    var birthplaceLatitude = -1.0

    @SerializedName("birthplace_longitude")
    var birthplaceLongitude = -1.0

    @SerializedName("birthplaceArea")
    var birthplaceArea: String? = ""

    @SerializedName("residence_latitude")
    var residenceLatitude: Double? = -1.0

    @SerializedName("residence_longitude")
    var residenceLongitude: Double? = -1.0

    @SerializedName("tag")
    var tag: String = ""

    @SerializedName("isHide")
    var isHide = false

    @SerializedName("isSelected")
    var isSelected = false

//    @SerializedName("horoscope")
//    var horoscope = ToOne<Horoscope>(this, BirthData_.horoscope)

    // Add BoxStore field
    @JvmField
    @Transient
    @Suppress("PropertyName")
    var __boxStore: BoxStore? = null

    constructor(time: Long) : this() {
        this.birthday = time
    }

    constructor(birthData: BirthData?) : this() {
        if (birthData != null) {
            this.name = birthData.name
            this.birthday = birthData.birthday
            this.isDaylightSavingTime = birthData.isDaylightSavingTime
            this.birthdayString = birthData.birthdayString
            this.birthplaceLatitude = birthData.birthplaceLatitude
            this.birthplaceLongitude = birthData.birthplaceLongitude
            this.birthplaceArea = birthData.birthplaceArea
            this.residenceLatitude = birthData.residenceLatitude
            this.residenceLongitude = birthData.residenceLongitude
            this.tag = birthData.tag
            this.isHide = birthData.isHide
        }
    }
    constructor(horoscope: Horoscope?) : this() {
        if (horoscope != null) {
            this.name = horoscope.name
            this.birthday = horoscope.birthdayTime
            this.birthplaceLatitude = horoscope.latLng.latitude
            this.birthplaceLongitude = horoscope.latLng.longitude
        }
    }

    fun generateBirthdayString(): String {
        if (birthday == 0L && !birthdayString.isNullOrEmpty()) {
            return birthdayString.toString()
        }
        // 轉換為 LocalDateTime（需要指定時區）
        val dateTimeMillis =
            LocalDateTime.ofInstant(Instant.ofEpochMilli(birthday), ZoneId.systemDefault())
        // 格式化為 "yyyy-MM-dd HH:mm:ss"
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
        LogUtil.d("時間戳轉換: ${dateTimeMillis.format(formatter)}")
        return dateTimeMillis.format(formatter)
    }

    fun calculate(context: Context): Horoscope {
        val horoscope = EphemerisUtil.calculate(
            context,
            Chart.Natal,
            name,
            birthday,
            LatLng(birthplaceLatitude, birthplaceLongitude)
        )
        return horoscope
    }

    constructor(parcel: Parcel) : this() {
        id = parcel.readLong()
        createTime = parcel.readLong()
        name = parcel.readString() ?: ""
        birthday = parcel.readLong()
        isDaylightSavingTime = parcel.readByte() != 0.toByte()
        birthdayString = parcel.readString()
        birthplaceLatitude = parcel.readDouble()
        birthplaceLongitude = parcel.readDouble()
        birthplaceArea = parcel.readString()
        residenceLatitude = parcel.readValue(Double::class.java.classLoader) as? Double
        residenceLongitude = parcel.readValue(Double::class.java.classLoader) as? Double
        tag = parcel.readString() ?: ""
        isHide = parcel.readByte() != 0.toByte()
        isSelected = parcel.readByte() != 0.toByte()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeLong(id)
        parcel.writeLong(createTime)
        parcel.writeString(name)
        parcel.writeLong(birthday)
        parcel.writeByte(if (isDaylightSavingTime) 1 else 0)
        parcel.writeString(birthdayString)
        parcel.writeDouble(birthplaceLatitude)
        parcel.writeDouble(birthplaceLongitude)
        parcel.writeString(birthplaceArea)
        parcel.writeValue(residenceLatitude)
        parcel.writeValue(residenceLongitude)
        parcel.writeString(tag)
        parcel.writeByte(if (isHide) 1 else 0)
        parcel.writeByte(if (isSelected) 1 else 0)
    }

    override fun describeContents(): Int = 0

    companion object CREATOR : Parcelable.Creator<BirthData> {
        override fun createFromParcel(parcel: Parcel): BirthData {
            return BirthData(parcel)
        }

        override fun newArray(size: Int): Array<BirthData?> {
            return arrayOfNulls(size)
        }
    }
}