<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />

    <!--要求位置存取權-->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32"
        tools:ignore="ScopedStorage" />

    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />

    <!-- Required to run keep-alive service when targeting API 28 or higher -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <application
        android:name=".App"
        android:allowBackup="false"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:ignore="DataExtractionRules"
        tools:targetApi="s">

        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/ga_ad_services_config"
            tools:replace="android:resource" />

        <activity
            android:name=".ui.activity.FeedbackActivity"
            android:screenOrientation="portrait"
            tools:ignore="LockedOrientationActivity" />

        <activity
            android:name=".ui.activity.MapActivity"
            android:screenOrientation="portrait"
            tools:ignore="LockedOrientationActivity" />

        <activity
            android:name=".ui.activity.QRCodeActivity"
            android:screenOrientation="portrait"
            tools:ignore="LockedOrientationActivity" />

        <activity
            android:name=".ui.activity.SplashActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/SplashTheme"
            tools:ignore="LockedOrientationActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.activity.MainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            tools:ignore="LockedOrientationActivity" />

        <activity
            android:name=".ui.activity.SignDetailActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            tools:ignore="LockedOrientationActivity" />

        <activity
            android:name=".ui.activity.SettingsActivity"
            android:screenOrientation="portrait"
            tools:ignore="LockedOrientationActivity" />

        <activity
            android:name=".ui.activity.SettingsComposeActivity"
            android:screenOrientation="portrait"
            tools:ignore="LockedOrientationActivity" />

        <activity
            android:name=".ui.activity.AnnouncementDetailActivity"
            android:screenOrientation="portrait"
            tools:ignore="LockedOrientationActivity" />

        <activity
            android:name=".ui.activity.DailyAstrologyDetailActivity"
            android:screenOrientation="portrait"
            tools:ignore="LockedOrientationActivity" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">

            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyCj5Cu0nmVdhEPgQqCPT41x33lPc7pek58" />

        <!-- Sample AdMob app ID: ca-app-pub-3940256099942544~********** -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-1800606262336792~**********" />


        <service
            android:name=".fcm.FcmService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- 解決屬性衝突 -->
        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/ga_ad_services_config"
            tools:replace="android:resource" />
    </application>

</manifest>